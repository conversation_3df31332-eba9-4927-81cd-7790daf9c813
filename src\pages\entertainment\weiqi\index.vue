<template>
  <view class="weiqi-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="game-title">围棋</text>
        <text class="game-subtitle">传统围棋游戏</text>
      </view>
    </view>
    
    <!-- 开发中提示 -->
    <view class="development-notice">
      <view class="notice-card">
        <view class="notice-icon">🚧</view>
        <text class="notice-title">功能开发中</text>
        <text class="notice-desc">围棋游戏正在紧张开发中，敬请期待！</text>
        
        <view class="features-preview">
          <text class="features-title">即将推出的功能：</text>
          <view class="feature-list">
            <view class="feature-item">
              <text class="feature-icon">🎯</text>
              <text class="feature-text">19x19标准棋盘</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🤖</text>
              <text class="feature-text">智能AI对弈</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📚</text>
              <text class="feature-text">围棋教学模式</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📊</text>
              <text class="feature-text">形势判断分析</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">💾</text>
              <text class="feature-text">棋谱保存回放</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🏆</text>
              <text class="feature-text">段位系统</text>
            </view>
          </view>
        </view>
        
        <view class="progress-section">
          <text class="progress-title">开发进度</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: '35%' }"></view>
          </view>
          <text class="progress-text">35% 完成</text>
        </view>
      </view>
    </view>
    
    <!-- 围棋规则介绍 -->
    <view class="rules-section">
      <view class="section-title">
        <text class="title-text">围棋简介</text>
      </view>
      
      <view class="rules-content">
        <view class="rule-item">
          <text class="rule-title">游戏目标</text>
          <text class="rule-desc">通过围地获得更多目数来获胜</text>
        </view>
        
        <view class="rule-item">
          <text class="rule-title">基本规则</text>
          <text class="rule-desc">黑白双方轮流在棋盘交叉点落子，不能在已有棋子的位置落子</text>
        </view>
        
        <view class="rule-item">
          <text class="rule-title">吃子规则</text>
          <text class="rule-desc">当对方棋子或棋块没有气时，可以将其提掉</text>
        </view>
        
        <view class="rule-item">
          <text class="rule-title">禁入点</text>
          <text class="rule-desc">不能在没有气且不能吃掉对方棋子的位置落子</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="actions">
      <button class="action-btn primary" @tap="goBack">
        <text class="btn-text">返回游戏列表</text>
      </button>
      
      <button class="action-btn" @tap="showNotification">
        <text class="btn-text">开发完成通知我</text>
      </button>
    </view>
    
    <!-- 预览棋盘 -->
    <view class="preview-board">
      <canvas 
        class="board-canvas" 
        canvas-id="previewBoard"
        :style="{ width: boardSize + 'px', height: boardSize + 'px' }"
      ></canvas>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import audioManager from '../../../utils/audio.js'

const boardSize = ref(200)
let canvasContext = null

onMounted(() => {
  initPreviewBoard()
})

// 初始化预览棋盘
const initPreviewBoard = async () => {
  await nextTick()
  
  canvasContext = uni.createCanvasContext('previewBoard')
  drawPreviewBoard()
}

// 绘制预览棋盘
const drawPreviewBoard = () => {
  if (!canvasContext) return
  
  const ctx = canvasContext
  const size = boardSize.value
  const lines = 9 // 简化为9x9显示
  const cellSize = size / (lines - 1)
  
  // 清空画布
  ctx.clearRect(0, 0, size, size)
  
  // 绘制背景
  ctx.fillStyle = '#DEB887'
  ctx.fillRect(0, 0, size, size)
  
  // 绘制网格线
  ctx.strokeStyle = '#8B4513'
  ctx.lineWidth = 1
  
  for (let i = 0; i < lines; i++) {
    const pos = i * cellSize
    
    // 竖线
    ctx.beginPath()
    ctx.moveTo(pos, 0)
    ctx.lineTo(pos, size)
    ctx.stroke()
    
    // 横线
    ctx.beginPath()
    ctx.moveTo(0, pos)
    ctx.lineTo(size, pos)
    ctx.stroke()
  }
  
  // 绘制星位
  const starPoints = [[2, 2], [2, 6], [4, 4], [6, 2], [6, 6]]
  ctx.fillStyle = '#8B4513'
  starPoints.forEach(([row, col]) => {
    const x = col * cellSize
    const y = row * cellSize
    ctx.beginPath()
    ctx.arc(x, y, 3, 0, 2 * Math.PI)
    ctx.fill()
  })
  
  // 绘制一些示例棋子
  const pieces = [
    [1, 1, 'black'], [1, 3, 'white'], [2, 2, 'black'],
    [3, 1, 'white'], [3, 3, 'black'], [4, 2, 'white']
  ]
  
  pieces.forEach(([row, col, color]) => {
    const x = col * cellSize
    const y = row * cellSize
    const radius = cellSize * 0.4
    
    // 绘制棋子阴影
    ctx.beginPath()
    ctx.arc(x + 1, y + 1, radius, 0, 2 * Math.PI)
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
    ctx.fill()
    
    // 绘制棋子
    ctx.beginPath()
    ctx.arc(x, y, radius, 0, 2 * Math.PI)
    
    if (color === 'black') {
      ctx.fillStyle = '#333'
      ctx.fill()
      ctx.strokeStyle = '#000'
    } else {
      ctx.fillStyle = '#FFF'
      ctx.fill()
      ctx.strokeStyle = '#333'
    }
    ctx.lineWidth = 1
    ctx.stroke()
  })
  
  ctx.draw()
}

// 返回游戏列表
const goBack = () => {
  audioManager.playSound('button_click')
  uni.navigateBack()
}

// 显示通知设置
const showNotification = () => {
  audioManager.playSound('click')
  uni.showModal({
    title: '开发通知',
    content: '我们会在围棋功能开发完成后第一时间通知您！',
    showCancel: false,
    confirmText: '好的'
  })
}
</script>

<style lang="scss" scoped>
.weiqi-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.header {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.header-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.game-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.game-subtitle {
  font-size: $font-size-md;
  opacity: 0.8;
}

.development-notice {
  padding: $spacing-md;
  margin-top: -40rpx;
  position: relative;
  z-index: 1;
}

.notice-card {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-lg;
  text-align: center;
}

.notice-icon {
  font-size: 120rpx;
  margin-bottom: $spacing-md;
}

.notice-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.notice-desc {
  font-size: $font-size-md;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-lg;
}

.features-preview {
  text-align: left;
  margin-bottom: $spacing-lg;
}

.features-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-md;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-sm;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: $bg-color-light;
  border-radius: $border-radius-md;
}

.feature-icon {
  font-size: $font-size-lg;
}

.feature-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.progress-section {
  text-align: center;
}

.progress-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: $bg-color-light;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: $spacing-sm;
}

.progress-fill {
  height: 100%;
  background: $primary-color;
  transition: width $transition-normal;
}

.progress-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.rules-section {
  padding: $spacing-md;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: white;
}

.rules-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  backdrop-filter: blur(10px);
}

.rule-item {
  margin-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.rule-title {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: white;
  display: block;
  margin-bottom: 5rpx;
}

.rule-desc {
  font-size: $font-size-sm;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  line-height: 1.5;
}

.actions {
  padding: $spacing-md;
  display: flex;
  gap: $spacing-md;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  
  &.primary {
    background: $primary-color;
    color: white;
  }
  
  &:not(.primary) {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2rpx solid rgba(255, 255, 255, 0.3);
  }
}

.preview-board {
  display: flex;
  justify-content: center;
  padding: $spacing-md;
}

.board-canvas {
  background: #DEB887;
  border-radius: $border-radius-md;
  box-shadow: $shadow-md;
}
</style>
