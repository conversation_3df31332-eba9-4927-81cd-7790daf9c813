import { defineStore } from 'pinia'

export const useRedPacketStore = defineStore('redpacket', {
  state: () => ({
    // 用户红包信息
    totalAmount: 0, // 总金额
    totalCount: 0, // 总红包数
    todayAmount: 0, // 今日获得
    
    // 签到信息
    signInDays: 0, // 连续签到天数
    lastSignInDate: null, // 最后签到日期
    todaySignedIn: false, // 今日是否已签到
    
    // 红包记录
    redPacketHistory: [], // 红包历史记录
    
    // 任务完成情况
    taskProgress: {
      useTools: 0, // 使用工具次数
      playGames: 0, // 游戏次数
      shareApp: 0, // 分享次数
    },
    
    // 红包配置
    redPacketConfig: {
      // 新用户红包
      newUser: {
        amount: [5, 10, 15], // 金额范围
        probability: 1.0 // 概率
      },
      // 签到红包
      signIn: {
        baseAmount: 1, // 基础金额
        incrementAmount: 0.5, // 递增金额
        maxAmount: 10, // 最大金额
        resetDays: 7 // 重置天数
      },
      // 任务红包
      task: {
        useTools: { amount: [1, 3], count: 5 }, // 使用工具5次
        playGames: { amount: [2, 5], count: 3 }, // 游戏3次
        shareApp: { amount: [3, 8], count: 1 } // 分享1次
      },
      // 随机红包
      random: {
        amount: [0.5, 2], // 金额范围
        probability: 0.1 // 10%概率
      }
    },
    
    // 红包弹窗状态
    showRedPacketModal: false,
    currentRedPacket: null,
    
    // 统计信息
    statistics: {
      totalReceived: 0, // 总共获得红包数
      totalAmount: 0, // 总共获得金额
      maxSingleAmount: 0, // 单次最大金额
      averageAmount: 0 // 平均金额
    }
  }),
  
  getters: {
    // 今日是否可以签到
    canSignInToday() {
      const today = new Date().toDateString()
      return !this.todaySignedIn && this.lastSignInDate !== today
    },
    
    // 下次签到奖励金额
    nextSignInAmount() {
      const config = this.redPacketConfig.signIn
      const amount = config.baseAmount + (this.signInDays * config.incrementAmount)
      return Math.min(amount, config.maxAmount)
    },
    
    // 任务完成情况
    taskCompletion() {
      const config = this.redPacketConfig.task
      return {
        useTools: {
          completed: this.taskProgress.useTools >= config.useTools.count,
          progress: this.taskProgress.useTools,
          total: config.useTools.count,
          reward: config.useTools.amount
        },
        playGames: {
          completed: this.taskProgress.playGames >= config.playGames.count,
          progress: this.taskProgress.playGames,
          total: config.playGames.count,
          reward: config.playGames.amount
        },
        shareApp: {
          completed: this.taskProgress.shareApp >= config.shareApp.count,
          progress: this.taskProgress.shareApp,
          total: config.shareApp.count,
          reward: config.shareApp.amount
        }
      }
    },
    
    // 今日红包统计
    todayStatistics() {
      const today = new Date().toDateString()
      const todayPackets = this.redPacketHistory.filter(packet => 
        new Date(packet.timestamp).toDateString() === today
      )
      
      return {
        count: todayPackets.length,
        amount: todayPackets.reduce((sum, packet) => sum + packet.amount, 0)
      }
    }
  },
  
  actions: {
    // 初始化用户数据
    initUserData() {
      const userData = uni.getStorageSync('redpacket_data')
      if (userData) {
        Object.assign(this.$state, userData)
      } else {
        // 新用户，发放欢迎红包
        this.giveNewUserRedPacket()
      }
      
      // 检查签到状态
      this.checkSignInStatus()
    },
    
    // 保存数据到本地存储
    saveData() {
      uni.setStorageSync('redpacket_data', this.$state)
    },
    
    // 新用户红包
    giveNewUserRedPacket() {
      const config = this.redPacketConfig.newUser
      const amount = this.getRandomAmount(config.amount)
      
      this.addRedPacket({
        type: 'newUser',
        amount: amount,
        title: '新用户红包',
        description: '欢迎使用多功能工具箱！'
      })
    },
    
    // 签到
    signIn() {
      if (!this.canSignInToday) return false
      
      const today = new Date().toDateString()
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()
      
      // 检查是否连续签到
      if (this.lastSignInDate === yesterday) {
        this.signInDays += 1
      } else {
        this.signInDays = 1
      }
      
      // 重置连续签到（7天后重置）
      if (this.signInDays > this.redPacketConfig.signIn.resetDays) {
        this.signInDays = 1
      }
      
      const amount = this.nextSignInAmount
      this.lastSignInDate = today
      this.todaySignedIn = true
      
      this.addRedPacket({
        type: 'signIn',
        amount: amount,
        title: `签到第${this.signInDays}天`,
        description: `连续签到${this.signInDays}天，获得红包奖励！`
      })
      
      this.saveData()
      return true
    },
    
    // 使用工具时触发
    onToolUsed() {
      this.taskProgress.useTools += 1
      this.checkTaskCompletion('useTools')
      this.checkRandomRedPacket()
      this.saveData()
    },
    
    // 游戏时触发
    onGamePlayed() {
      this.taskProgress.playGames += 1
      this.checkTaskCompletion('playGames')
      this.checkRandomRedPacket()
      this.saveData()
    },
    
    // 分享时触发
    onAppShared() {
      this.taskProgress.shareApp += 1
      this.checkTaskCompletion('shareApp')
      this.saveData()
    },
    
    // 检查任务完成
    checkTaskCompletion(taskType) {
      const completion = this.taskCompletion[taskType]
      if (completion.completed && !this.isTaskRewardClaimed(taskType)) {
        const amount = this.getRandomAmount(completion.reward)
        this.addRedPacket({
          type: 'task',
          taskType: taskType,
          amount: amount,
          title: '任务红包',
          description: `完成${this.getTaskName(taskType)}任务，获得红包奖励！`
        })
        
        // 标记任务奖励已领取
        this.markTaskRewardClaimed(taskType)
      }
    },
    
    // 检查随机红包
    checkRandomRedPacket() {
      const config = this.redPacketConfig.random
      if (Math.random() < config.probability) {
        const amount = this.getRandomAmount(config.amount)
        this.addRedPacket({
          type: 'random',
          amount: amount,
          title: '惊喜红包',
          description: '恭喜你获得惊喜红包！'
        })
      }
    },
    
    // 添加红包
    addRedPacket(redPacket) {
      const packet = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        claimed: false,
        ...redPacket
      }
      
      this.redPacketHistory.unshift(packet)
      this.currentRedPacket = packet
      this.showRedPacketModal = true
      
      // 更新统计
      this.updateStatistics(packet)
    },
    
    // 领取红包
    claimRedPacket(packetId) {
      const packet = this.redPacketHistory.find(p => p.id === packetId)
      if (packet && !packet.claimed) {
        packet.claimed = true
        this.totalAmount += packet.amount
        this.totalCount += 1
        this.todayAmount += packet.amount
        
        this.saveData()
        return true
      }
      return false
    },
    
    // 关闭红包弹窗
    closeRedPacketModal() {
      this.showRedPacketModal = false
      this.currentRedPacket = null
    },
    
    // 工具方法
    getRandomAmount(range) {
      const [min, max] = range
      return Math.round((Math.random() * (max - min) + min) * 100) / 100
    },
    
    getTaskName(taskType) {
      const names = {
        useTools: '使用工具',
        playGames: '游戏娱乐',
        shareApp: '分享应用'
      }
      return names[taskType] || taskType
    },
    
    isTaskRewardClaimed(taskType) {
      const today = new Date().toDateString()
      return this.redPacketHistory.some(packet => 
        packet.type === 'task' && 
        packet.taskType === taskType && 
        new Date(packet.timestamp).toDateString() === today
      )
    },
    
    markTaskRewardClaimed(taskType) {
      // 任务奖励通过添加红包记录来标记
    },
    
    checkSignInStatus() {
      const today = new Date().toDateString()
      this.todaySignedIn = this.lastSignInDate === today
      
      // 重置今日金额（每天重置）
      const lastResetDate = uni.getStorageSync('last_reset_date')
      if (lastResetDate !== today) {
        this.todayAmount = 0
        this.taskProgress = { useTools: 0, playGames: 0, shareApp: 0 }
        uni.setStorageSync('last_reset_date', today)
        this.saveData()
      }
    },
    
    updateStatistics(packet) {
      this.statistics.totalReceived += 1
      this.statistics.totalAmount += packet.amount
      this.statistics.maxSingleAmount = Math.max(this.statistics.maxSingleAmount, packet.amount)
      this.statistics.averageAmount = this.statistics.totalAmount / this.statistics.totalReceived
    }
  }
})
