/**
 * 五子棋游戏引擎
 * 包含棋盘表示、胜负判断、AI算法等核心功能
 */

class GobangEngine {
  constructor() {
    this.boardSize = 15
    this.winLength = 5
    
    // 棋子类型
    this.EMPTY = 0
    this.BLACK = 1
    this.WHITE = 2
    
    // 方向向量 (用于检查连子)
    this.directions = [
      [0, 1],   // 水平
      [1, 0],   // 垂直
      [1, 1],   // 主对角线
      [1, -1]   // 副对角线
    ]
    
    // 评估分数
    this.scores = {
      FIVE: 100000,      // 五连
      OPEN_FOUR: 10000,  // 活四
      FOUR: 1000,        // 冲四
      OPEN_THREE: 1000,  // 活三
      THREE: 100,        // 眠三
      OPEN_TWO: 100,     // 活二
      TWO: 10,           // 眠二
      ONE: 1             // 单子
    }
    
    this.reset()
  }
  
  // 重置游戏
  reset() {
    this.board = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(this.EMPTY))
    this.currentPlayer = this.BLACK
    this.moveHistory = []
    this.gameOver = false
    this.winner = null
    this.winningLine = null
  }
  
  // 检查坐标是否有效
  isValidPosition(row, col) {
    return row >= 0 && row < this.boardSize && col >= 0 && col < this.boardSize
  }
  
  // 检查位置是否为空
  isEmpty(row, col) {
    return this.isValidPosition(row, col) && this.board[row][col] === this.EMPTY
  }
  
  // 下棋
  makeMove(row, col, player = this.currentPlayer) {
    if (!this.isEmpty(row, col) || this.gameOver) {
      return false
    }
    
    // 记录走法
    this.moveHistory.push({
      row,
      col,
      player,
      timestamp: Date.now()
    })
    
    // 放置棋子
    this.board[row][col] = player
    
    // 检查胜负
    if (this.checkWin(row, col, player)) {
      this.gameOver = true
      this.winner = player
    }
    
    // 检查平局
    if (!this.gameOver && this.isBoardFull()) {
      this.gameOver = true
      this.winner = null // 平局
    }
    
    // 切换玩家
    this.currentPlayer = player === this.BLACK ? this.WHITE : this.BLACK
    
    return true
  }
  
  // 撤销走法
  undoMove() {
    if (this.moveHistory.length === 0) return false
    
    const lastMove = this.moveHistory.pop()
    this.board[lastMove.row][lastMove.col] = this.EMPTY
    
    // 恢复游戏状态
    this.currentPlayer = lastMove.player
    this.gameOver = false
    this.winner = null
    this.winningLine = null
    
    return true
  }
  
  // 检查胜利条件
  checkWin(row, col, player) {
    for (const [dr, dc] of this.directions) {
      let count = 1 // 包含当前棋子
      const line = [[row, col]]
      
      // 向正方向检查
      for (let i = 1; i < this.winLength; i++) {
        const newRow = row + dr * i
        const newCol = col + dc * i
        
        if (this.isValidPosition(newRow, newCol) && this.board[newRow][newCol] === player) {
          count++
          line.push([newRow, newCol])
        } else {
          break
        }
      }
      
      // 向负方向检查
      for (let i = 1; i < this.winLength; i++) {
        const newRow = row - dr * i
        const newCol = col - dc * i
        
        if (this.isValidPosition(newRow, newCol) && this.board[newRow][newCol] === player) {
          count++
          line.unshift([newRow, newCol])
        } else {
          break
        }
      }
      
      if (count >= this.winLength) {
        this.winningLine = line.slice(0, this.winLength)
        return true
      }
    }
    
    return false
  }
  
  // 检查棋盘是否已满
  isBoardFull() {
    for (let row = 0; row < this.boardSize; row++) {
      for (let col = 0; col < this.boardSize; col++) {
        if (this.board[row][col] === this.EMPTY) {
          return false
        }
      }
    }
    return true
  }
  
  // 获取所有可能的走法
  getAvailableMoves() {
    const moves = []
    
    for (let row = 0; row < this.boardSize; row++) {
      for (let col = 0; col < this.boardSize; col++) {
        if (this.board[row][col] === this.EMPTY) {
          moves.push([row, col])
        }
      }
    }
    
    return moves
  }
  
  // 获取有效的走法（在已有棋子附近）
  getValidMoves(range = 2) {
    const moves = []
    const visited = new Set()
    
    // 如果棋盘为空，返回中心位置
    if (this.moveHistory.length === 0) {
      const center = Math.floor(this.boardSize / 2)
      return [[center, center]]
    }
    
    // 在已有棋子附近寻找空位
    for (let row = 0; row < this.boardSize; row++) {
      for (let col = 0; col < this.boardSize; col++) {
        if (this.board[row][col] !== this.EMPTY) {
          // 检查周围的空位
          for (let dr = -range; dr <= range; dr++) {
            for (let dc = -range; dc <= range; dc++) {
              const newRow = row + dr
              const newCol = col + dc
              const key = `${newRow},${newCol}`
              
              if (this.isEmpty(newRow, newCol) && !visited.has(key)) {
                moves.push([newRow, newCol])
                visited.add(key)
              }
            }
          }
        }
      }
    }
    
    return moves
  }
  
  // 评估单个位置的分数
  evaluatePosition(row, col, player) {
    let score = 0
    
    for (const [dr, dc] of this.directions) {
      const lineScore = this.evaluateLine(row, col, dr, dc, player)
      score += lineScore
    }
    
    return score
  }
  
  // 评估一条线的分数
  evaluateLine(row, col, dr, dc, player) {
    let count = 1 // 包含当前位置
    let openEnds = 0 // 开放端点数
    let blocked = false
    
    // 向正方向检查
    let i = 1
    while (i < this.winLength) {
      const newRow = row + dr * i
      const newCol = col + dc * i
      
      if (!this.isValidPosition(newRow, newCol)) {
        blocked = true
        break
      }
      
      if (this.board[newRow][newCol] === player) {
        count++
        i++
      } else if (this.board[newRow][newCol] === this.EMPTY) {
        openEnds++
        break
      } else {
        blocked = true
        break
      }
    }
    
    // 向负方向检查
    i = 1
    while (i < this.winLength) {
      const newRow = row - dr * i
      const newCol = col - dc * i
      
      if (!this.isValidPosition(newRow, newCol)) {
        break
      }
      
      if (this.board[newRow][newCol] === player) {
        count++
        i++
      } else if (this.board[newRow][newCol] === this.EMPTY) {
        if (!blocked) openEnds++
        break
      } else {
        break
      }
    }
    
    return this.getPatternScore(count, openEnds)
  }
  
  // 根据棋型获取分数
  getPatternScore(count, openEnds) {
    if (count >= 5) return this.scores.FIVE
    
    switch (count) {
      case 4:
        return openEnds >= 1 ? this.scores.OPEN_FOUR : this.scores.FOUR
      case 3:
        return openEnds >= 2 ? this.scores.OPEN_THREE : this.scores.THREE
      case 2:
        return openEnds >= 2 ? this.scores.OPEN_TWO : this.scores.TWO
      case 1:
        return this.scores.ONE
      default:
        return 0
    }
  }
  
  // 评估整个棋盘
  evaluate() {
    let blackScore = 0
    let whiteScore = 0
    
    for (let row = 0; row < this.boardSize; row++) {
      for (let col = 0; col < this.boardSize; col++) {
        if (this.board[row][col] === this.BLACK) {
          blackScore += this.evaluatePosition(row, col, this.BLACK)
        } else if (this.board[row][col] === this.WHITE) {
          whiteScore += this.evaluatePosition(row, col, this.WHITE)
        }
      }
    }
    
    return blackScore - whiteScore
  }
  
  // Minimax算法获取最佳走法
  getBestMove(depth = 3, isMaximizing = true, alpha = -Infinity, beta = Infinity) {
    if (depth === 0 || this.gameOver) {
      return { score: this.evaluate(), move: null }
    }
    
    const moves = this.getValidMoves()
    if (moves.length === 0) {
      return { score: 0, move: null }
    }
    
    // 移动排序优化
    const sortedMoves = this.sortMoves(moves)
    
    let bestMove = null
    let bestScore = isMaximizing ? -Infinity : Infinity
    
    for (const [row, col] of sortedMoves) {
      // 模拟走法
      const originalBoard = this.board.map(row => [...row])
      const originalPlayer = this.currentPlayer
      const originalGameOver = this.gameOver
      const originalWinner = this.winner
      
      this.makeMove(row, col, isMaximizing ? this.WHITE : this.BLACK)
      
      const result = this.getBestMove(depth - 1, !isMaximizing, alpha, beta)
      const score = result.score
      
      // 恢复状态
      this.board = originalBoard
      this.currentPlayer = originalPlayer
      this.gameOver = originalGameOver
      this.winner = originalWinner
      this.moveHistory.pop()
      
      if (isMaximizing) {
        if (score > bestScore) {
          bestScore = score
          bestMove = [row, col]
        }
        alpha = Math.max(alpha, score)
      } else {
        if (score < bestScore) {
          bestScore = score
          bestMove = [row, col]
        }
        beta = Math.min(beta, score)
      }
      
      // Alpha-Beta剪枝
      if (beta <= alpha) {
        break
      }
    }
    
    return { score: bestScore, move: bestMove }
  }
  
  // 移动排序（优先考虑中心和高分位置）
  sortMoves(moves) {
    const center = Math.floor(this.boardSize / 2)
    
    return moves.sort((a, b) => {
      const [rowA, colA] = a
      const [rowB, colB] = b
      
      // 计算到中心的距离
      const distA = Math.abs(rowA - center) + Math.abs(colA - center)
      const distB = Math.abs(rowB - center) + Math.abs(colB - center)
      
      // 计算位置评分
      const scoreA = this.evaluatePosition(rowA, colA, this.WHITE)
      const scoreB = this.evaluatePosition(rowB, colB, this.WHITE)
      
      // 优先考虑高分位置，其次考虑中心位置
      if (scoreA !== scoreB) {
        return scoreB - scoreA
      }
      return distA - distB
    })
  }
  
  // 获取AI走法
  getAIMove(difficulty = 'medium') {
    const depths = {
      easy: 2,
      medium: 3,
      hard: 4
    }
    
    const depth = depths[difficulty] || 3
    
    // 检查是否有必胜或必防的走法
    const criticalMove = this.findCriticalMove()
    if (criticalMove) {
      return criticalMove
    }
    
    const result = this.getBestMove(depth, true)
    return result.move
  }
  
  // 寻找关键走法（必胜或必防）
  findCriticalMove() {
    const moves = this.getValidMoves()
    
    // 检查是否有必胜走法
    for (const [row, col] of moves) {
      this.board[row][col] = this.WHITE
      if (this.checkWin(row, col, this.WHITE)) {
        this.board[row][col] = this.EMPTY
        return [row, col]
      }
      this.board[row][col] = this.EMPTY
    }
    
    // 检查是否需要防守
    for (const [row, col] of moves) {
      this.board[row][col] = this.BLACK
      if (this.checkWin(row, col, this.BLACK)) {
        this.board[row][col] = this.EMPTY
        return [row, col]
      }
      this.board[row][col] = this.EMPTY
    }
    
    return null
  }
  
  // 获取棋盘状态
  getBoardState() {
    return {
      board: this.board.map(row => [...row]),
      currentPlayer: this.currentPlayer,
      gameOver: this.gameOver,
      winner: this.winner,
      winningLine: this.winningLine,
      moveCount: this.moveHistory.length
    }
  }
  
  // 获取玩家名称
  getPlayerName(player) {
    switch (player) {
      case this.BLACK: return '黑方'
      case this.WHITE: return '白方'
      default: return '无'
    }
  }
  
  // 复制棋盘
  copyBoard() {
    return this.board.map(row => [...row])
  }
}

export default GobangEngine
