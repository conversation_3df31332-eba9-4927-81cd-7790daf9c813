/**
 * 单位转换器工具类
 * 支持长度、重量、温度、面积、体积、时间等单位转换
 */

class UnitConverter {
  constructor() {
    // 单位转换配置
    this.conversions = {
      // 长度单位 (基准单位: 米)
      length: {
        name: '长度',
        icon: '📏',
        baseUnit: 'm',
        units: {
          'mm': { name: '毫米', factor: 0.001 },
          'cm': { name: '厘米', factor: 0.01 },
          'dm': { name: '分米', factor: 0.1 },
          'm': { name: '米', factor: 1 },
          'km': { name: '千米', factor: 1000 },
          'in': { name: '英寸', factor: 0.0254 },
          'ft': { name: '英尺', factor: 0.3048 },
          'yd': { name: '码', factor: 0.9144 },
          'mi': { name: '英里', factor: 1609.344 },
          'nmi': { name: '海里', factor: 1852 }
        }
      },
      
      // 重量单位 (基准单位: 千克)
      weight: {
        name: '重量',
        icon: '⚖️',
        baseUnit: 'kg',
        units: {
          'mg': { name: '毫克', factor: 0.000001 },
          'g': { name: '克', factor: 0.001 },
          'kg': { name: '千克', factor: 1 },
          't': { name: '吨', factor: 1000 },
          'oz': { name: '盎司', factor: 0.0283495 },
          'lb': { name: '磅', factor: 0.453592 },
          'st': { name: '英石', factor: 6.35029 },
          'jin': { name: '斤', factor: 0.5 },
          'liang': { name: '两', factor: 0.05 }
        }
      },
      
      // 面积单位 (基准单位: 平方米)
      area: {
        name: '面积',
        icon: '📐',
        baseUnit: 'm²',
        units: {
          'mm²': { name: '平方毫米', factor: 0.000001 },
          'cm²': { name: '平方厘米', factor: 0.0001 },
          'm²': { name: '平方米', factor: 1 },
          'km²': { name: '平方千米', factor: 1000000 },
          'ha': { name: '公顷', factor: 10000 },
          'acre': { name: '英亩', factor: 4046.86 },
          'ft²': { name: '平方英尺', factor: 0.092903 },
          'in²': { name: '平方英寸', factor: 0.00064516 },
          'mu': { name: '亩', factor: 666.667 }
        }
      },
      
      // 体积单位 (基准单位: 升)
      volume: {
        name: '体积',
        icon: '🥤',
        baseUnit: 'L',
        units: {
          'ml': { name: '毫升', factor: 0.001 },
          'L': { name: '升', factor: 1 },
          'm³': { name: '立方米', factor: 1000 },
          'cm³': { name: '立方厘米', factor: 0.001 },
          'fl oz': { name: '液体盎司', factor: 0.0295735 },
          'cup': { name: '杯', factor: 0.236588 },
          'pt': { name: '品脱', factor: 0.473176 },
          'qt': { name: '夸脱', factor: 0.946353 },
          'gal': { name: '加仑', factor: 3.78541 }
        }
      },
      
      // 时间单位 (基准单位: 秒)
      time: {
        name: '时间',
        icon: '⏰',
        baseUnit: 's',
        units: {
          'ms': { name: '毫秒', factor: 0.001 },
          's': { name: '秒', factor: 1 },
          'min': { name: '分钟', factor: 60 },
          'h': { name: '小时', factor: 3600 },
          'd': { name: '天', factor: 86400 },
          'w': { name: '周', factor: 604800 },
          'month': { name: '月', factor: 2629746 },
          'year': { name: '年', factor: 31556952 }
        }
      },
      
      // 速度单位 (基准单位: 米/秒)
      speed: {
        name: '速度',
        icon: '🚗',
        baseUnit: 'm/s',
        units: {
          'm/s': { name: '米/秒', factor: 1 },
          'km/h': { name: '千米/小时', factor: 0.277778 },
          'mph': { name: '英里/小时', factor: 0.44704 },
          'ft/s': { name: '英尺/秒', factor: 0.3048 },
          'knot': { name: '节', factor: 0.514444 }
        }
      },
      
      // 数据存储单位 (基准单位: 字节)
      data: {
        name: '数据存储',
        icon: '💾',
        baseUnit: 'B',
        units: {
          'B': { name: '字节', factor: 1 },
          'KB': { name: '千字节', factor: 1024 },
          'MB': { name: '兆字节', factor: 1048576 },
          'GB': { name: '吉字节', factor: 1073741824 },
          'TB': { name: '太字节', factor: 1099511627776 },
          'PB': { name: '拍字节', factor: 1125899906842624 }
        }
      },
      
      // 能量单位 (基准单位: 焦耳)
      energy: {
        name: '能量',
        icon: '⚡',
        baseUnit: 'J',
        units: {
          'J': { name: '焦耳', factor: 1 },
          'kJ': { name: '千焦', factor: 1000 },
          'cal': { name: '卡路里', factor: 4.184 },
          'kcal': { name: '千卡', factor: 4184 },
          'Wh': { name: '瓦时', factor: 3600 },
          'kWh': { name: '千瓦时', factor: 3600000 },
          'BTU': { name: '英热单位', factor: 1055.06 }
        }
      }
    }
    
    // 温度转换需要特殊处理
    this.temperatureConversions = {
      name: '温度',
      icon: '🌡️',
      units: ['°C', '°F', 'K', '°R']
    }
    
    this.history = []
  }
  
  /**
   * 获取所有转换类型
   */
  getConversionTypes() {
    const types = Object.keys(this.conversions).map(key => ({
      key,
      name: this.conversions[key].name,
      icon: this.conversions[key].icon
    }))
    
    // 添加温度转换
    types.push({
      key: 'temperature',
      name: this.temperatureConversions.name,
      icon: this.temperatureConversions.icon
    })
    
    return types
  }
  
  /**
   * 获取指定类型的单位列表
   */
  getUnits(type) {
    if (type === 'temperature') {
      return this.temperatureConversions.units.map(unit => ({
        key: unit,
        name: unit
      }))
    }
    
    if (!this.conversions[type]) {
      throw new Error(`不支持的转换类型: ${type}`)
    }
    
    const units = this.conversions[type].units
    return Object.keys(units).map(key => ({
      key,
      name: units[key].name
    }))
  }
  
  /**
   * 执行单位转换
   */
  convert(value, fromUnit, toUnit, type) {
    try {
      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        throw new Error('请输入有效的数值')
      }
      
      let result
      
      if (type === 'temperature') {
        result = this.convertTemperature(numValue, fromUnit, toUnit)
      } else {
        result = this.convertStandard(numValue, fromUnit, toUnit, type)
      }
      
      // 添加到历史记录
      this.addToHistory({
        value: numValue,
        fromUnit,
        toUnit,
        result,
        type,
        timestamp: new Date().toISOString()
      })
      
      return {
        success: true,
        result: this.formatResult(result),
        rawResult: result
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 标准单位转换
   */
  convertStandard(value, fromUnit, toUnit, type) {
    if (!this.conversions[type]) {
      throw new Error(`不支持的转换类型: ${type}`)
    }
    
    const units = this.conversions[type].units
    
    if (!units[fromUnit]) {
      throw new Error(`不支持的源单位: ${fromUnit}`)
    }
    
    if (!units[toUnit]) {
      throw new Error(`不支持的目标单位: ${toUnit}`)
    }
    
    // 转换为基准单位
    const baseValue = value * units[fromUnit].factor
    
    // 从基准单位转换为目标单位
    const result = baseValue / units[toUnit].factor
    
    return result
  }
  
  /**
   * 温度转换
   */
  convertTemperature(value, fromUnit, toUnit) {
    if (fromUnit === toUnit) {
      return value
    }
    
    // 先转换为摄氏度
    let celsius
    switch (fromUnit) {
      case '°C':
        celsius = value
        break
      case '°F':
        celsius = (value - 32) * 5 / 9
        break
      case 'K':
        celsius = value - 273.15
        break
      case '°R':
        celsius = (value - 491.67) * 5 / 9
        break
      default:
        throw new Error(`不支持的温度单位: ${fromUnit}`)
    }
    
    // 从摄氏度转换为目标单位
    switch (toUnit) {
      case '°C':
        return celsius
      case '°F':
        return celsius * 9 / 5 + 32
      case 'K':
        return celsius + 273.15
      case '°R':
        return celsius * 9 / 5 + 491.67
      default:
        throw new Error(`不支持的温度单位: ${toUnit}`)
    }
  }
  
  /**
   * 格式化结果
   */
  formatResult(result) {
    // 处理非常小的数
    if (Math.abs(result) < 1e-10) {
      return '0'
    }
    
    // 处理整数
    if (Number.isInteger(result)) {
      return result.toString()
    }
    
    // 处理小数
    const str = result.toString()
    if (str.includes('e')) {
      // 科学计数法
      return result.toExponential(6)
    } else {
      // 普通小数，最多保留8位小数
      return parseFloat(result.toFixed(8)).toString()
    }
  }
  
  /**
   * 批量转换
   */
  convertBatch(value, fromUnit, type, targetUnits = null) {
    const results = []
    
    if (type === 'temperature') {
      const units = targetUnits || this.temperatureConversions.units.filter(u => u !== fromUnit)
      
      for (const unit of units) {
        const result = this.convert(value, fromUnit, unit, type)
        if (result.success) {
          results.push({
            unit,
            value: result.result,
            rawValue: result.rawResult
          })
        }
      }
    } else {
      if (!this.conversions[type]) {
        throw new Error(`不支持的转换类型: ${type}`)
      }
      
      const units = this.conversions[type].units
      const unitKeys = targetUnits || Object.keys(units).filter(u => u !== fromUnit)
      
      for (const unit of unitKeys) {
        const result = this.convert(value, fromUnit, unit, type)
        if (result.success) {
          results.push({
            unit,
            unitName: units[unit].name,
            value: result.result,
            rawValue: result.rawResult
          })
        }
      }
    }
    
    return results
  }
  
  /**
   * 添加到历史记录
   */
  addToHistory(record) {
    this.history.unshift(record)
    
    // 只保留最近50条记录
    if (this.history.length > 50) {
      this.history = this.history.slice(0, 50)
    }
  }
  
  /**
   * 获取历史记录
   */
  getHistory() {
    return this.history
  }
  
  /**
   * 清空历史记录
   */
  clearHistory() {
    this.history = []
  }
  
  /**
   * 获取单位信息
   */
  getUnitInfo(unit, type) {
    if (type === 'temperature') {
      return { name: unit }
    }
    
    if (!this.conversions[type] || !this.conversions[type].units[unit]) {
      return null
    }
    
    return this.conversions[type].units[unit]
  }
  
  /**
   * 搜索单位
   */
  searchUnits(query, type = null) {
    const results = []
    
    const searchInType = (typeKey, typeData) => {
      if (typeKey === 'temperature') {
        this.temperatureConversions.units.forEach(unit => {
          if (unit.toLowerCase().includes(query.toLowerCase())) {
            results.push({
              type: typeKey,
              typeName: this.temperatureConversions.name,
              unit,
              unitName: unit
            })
          }
        })
      } else {
        Object.keys(typeData.units).forEach(unitKey => {
          const unitData = typeData.units[unitKey]
          if (
            unitKey.toLowerCase().includes(query.toLowerCase()) ||
            unitData.name.toLowerCase().includes(query.toLowerCase())
          ) {
            results.push({
              type: typeKey,
              typeName: typeData.name,
              unit: unitKey,
              unitName: unitData.name
            })
          }
        })
      }
    }
    
    if (type) {
      if (type === 'temperature') {
        searchInType('temperature', null)
      } else if (this.conversions[type]) {
        searchInType(type, this.conversions[type])
      }
    } else {
      // 搜索所有类型
      Object.keys(this.conversions).forEach(typeKey => {
        searchInType(typeKey, this.conversions[typeKey])
      })
      searchInType('temperature', null)
    }
    
    return results
  }
  
  /**
   * 获取常用转换
   */
  getCommonConversions() {
    return [
      { type: 'length', from: 'm', to: 'ft', name: '米 → 英尺' },
      { type: 'length', from: 'km', to: 'mi', name: '千米 → 英里' },
      { type: 'weight', from: 'kg', to: 'lb', name: '千克 → 磅' },
      { type: 'temperature', from: '°C', to: '°F', name: '摄氏度 → 华氏度' },
      { type: 'volume', from: 'L', to: 'gal', name: '升 → 加仑' },
      { type: 'data', from: 'GB', to: 'MB', name: 'GB → MB' }
    ]
  }
  
  /**
   * 验证输入值
   */
  validateInput(value, type) {
    const numValue = parseFloat(value)
    
    if (isNaN(numValue)) {
      return { valid: false, error: '请输入有效的数值' }
    }
    
    // 特殊验证规则
    if (type === 'temperature') {
      // 温度的物理限制
      if (numValue < -273.15 && value.includes('°C')) {
        return { valid: false, error: '温度不能低于绝对零度(-273.15°C)' }
      }
      if (numValue < 0 && value.includes('K')) {
        return { valid: false, error: '开尔文温度不能为负数' }
      }
    }
    
    // 负数检查（某些单位不允许负数）
    const noNegativeTypes = ['data', 'energy']
    if (noNegativeTypes.includes(type) && numValue < 0) {
      return { valid: false, error: '该单位类型不支持负数' }
    }
    
    return { valid: true }
  }
}

// 创建全局实例
const unitConverter = new UnitConverter()

export default unitConverter
