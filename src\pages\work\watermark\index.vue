<template>
  <view class="watermark-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="page-title">去水印工具</text>
        <text class="page-desc">图片视频去水印处理</text>
      </view>
    </view>
    
    <!-- 开发中提示 -->
    <view class="development-notice">
      <view class="notice-card">
        <view class="notice-icon">🚧</view>
        <text class="notice-title">功能开发中</text>
        <text class="notice-desc">去水印工具正在紧张开发中，敬请期待！</text>
        
        <view class="features-preview">
          <text class="features-title">即将推出的功能：</text>
          <view class="feature-list">
            <view class="feature-item">
              <text class="feature-icon">🖼️</text>
              <text class="feature-text">图片去水印</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🎬</text>
              <text class="feature-text">视频去水印</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🔍</text>
              <text class="feature-text">智能识别</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">⚡</text>
              <text class="feature-text">快速处理</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📱</text>
              <text class="feature-text">批量处理</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">💾</text>
              <text class="feature-text">高质量输出</text>
            </view>
          </view>
        </view>
        
        <view class="progress-section">
          <text class="progress-title">开发进度</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: '15%' }"></view>
          </view>
          <text class="progress-text">15% 完成</text>
        </view>
      </view>
    </view>
    
    <!-- 功能介绍 -->
    <view class="intro-section">
      <view class="section-title">
        <text class="title-text">功能介绍</text>
      </view>
      
      <view class="intro-content">
        <view class="intro-item">
          <view class="intro-icon">🖼️</view>
          <view class="intro-info">
            <text class="intro-title">图片去水印</text>
            <text class="intro-desc">支持JPG、PNG等格式，智能识别并去除图片中的水印</text>
          </view>
        </view>
        
        <view class="intro-item">
          <view class="intro-icon">🎬</view>
          <view class="intro-info">
            <text class="intro-title">视频去水印</text>
            <text class="intro-desc">支持MP4、AVI等格式，去除视频中的固定位置水印</text>
          </view>
        </view>
        
        <view class="intro-item">
          <view class="intro-icon">🔍</view>
          <view class="intro-info">
            <text class="intro-title">智能识别</text>
            <text class="intro-desc">AI算法自动识别水印位置，无需手动标记</text>
          </view>
        </view>
        
        <view class="intro-item">
          <view class="intro-icon">⚡</view>
          <view class="intro-info">
            <text class="intro-title">快速处理</text>
            <text class="intro-desc">优化算法，快速完成去水印处理</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="actions">
      <button class="action-btn primary" @tap="goBack">
        <text class="btn-text">返回工具列表</text>
      </button>
      
      <button class="action-btn" @tap="showNotification">
        <text class="btn-text">开发完成通知我</text>
      </button>
    </view>
    
    <!-- 预览界面 -->
    <view class="preview-section">
      <view class="preview-card">
        <view class="preview-header">
          <text class="preview-title">界面预览</text>
        </view>
        
        <view class="preview-content">
          <view class="upload-area">
            <view class="upload-icon">📁</view>
            <text class="upload-text">点击上传图片/视频</text>
            <text class="upload-desc">支持JPG、PNG、MP4等格式</text>
          </view>
          
          <view class="process-area">
            <view class="process-step">
              <view class="step-number">1</view>
              <text class="step-text">上传文件</text>
            </view>
            <view class="process-arrow">→</view>
            <view class="process-step">
              <view class="step-number">2</view>
              <text class="step-text">智能识别</text>
            </view>
            <view class="process-arrow">→</view>
            <view class="process-step">
              <view class="step-number">3</view>
              <text class="step-text">去除水印</text>
            </view>
            <view class="process-arrow">→</view>
            <view class="process-step">
              <view class="step-number">4</view>
              <text class="step-text">下载结果</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import audioManager from '../../../utils/audio.js'

// 返回工具列表
const goBack = () => {
  audioManager.playSound('button_click')
  uni.navigateBack()
}

// 显示通知设置
const showNotification = () => {
  audioManager.playSound('click')
  uni.showModal({
    title: '开发通知',
    content: '我们会在去水印功能开发完成后第一时间通知您！',
    showCancel: false,
    confirmText: '好的'
  })
}
</script>

<style lang="scss" scoped>
.watermark-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.header {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.header-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  font-size: $font-size-md;
  opacity: 0.8;
}

.development-notice {
  padding: $spacing-md;
  margin-top: -40rpx;
  position: relative;
  z-index: 1;
}

.notice-card {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-lg;
  text-align: center;
}

.notice-icon {
  font-size: 120rpx;
  margin-bottom: $spacing-md;
}

.notice-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.notice-desc {
  font-size: $font-size-md;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-lg;
}

.features-preview {
  text-align: left;
  margin-bottom: $spacing-lg;
}

.features-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-md;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-sm;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: $bg-color-light;
  border-radius: $border-radius-md;
}

.feature-icon {
  font-size: $font-size-lg;
}

.feature-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.progress-section {
  text-align: center;
}

.progress-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: $bg-color-light;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: $spacing-sm;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  transition: width $transition-normal;
}

.progress-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.intro-section, .preview-section {
  padding: $spacing-md;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: white;
}

.intro-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  backdrop-filter: blur(10px);
}

.intro-item {
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
  margin-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.intro-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
  flex-shrink: 0;
}

.intro-info {
  flex: 1;
}

.intro-title {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: white;
  display: block;
  margin-bottom: 5rpx;
}

.intro-desc {
  font-size: $font-size-sm;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  line-height: 1.5;
}

.actions {
  padding: $spacing-md;
  display: flex;
  gap: $spacing-md;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  
  &.primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }
  
  &:not(.primary) {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2rpx solid rgba(255, 255, 255, 0.3);
  }
}

.preview-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  backdrop-filter: blur(10px);
}

.preview-header {
  margin-bottom: $spacing-lg;
}

.preview-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: white;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.upload-area {
  text-align: center;
  padding: $spacing-xl;
  border: 2rpx dashed rgba(255, 255, 255, 0.3);
  border-radius: $border-radius-lg;
  background: rgba(255, 255, 255, 0.05);
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: $spacing-md;
}

.upload-text {
  font-size: $font-size-lg;
  color: white;
  display: block;
  margin-bottom: $spacing-sm;
}

.upload-desc {
  font-size: $font-size-sm;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

.process-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-sm;
  flex: 1;
  min-width: 120rpx;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: white;
}

.step-text {
  font-size: $font-size-sm;
  color: white;
  text-align: center;
}

.process-arrow {
  font-size: $font-size-lg;
  color: rgba(255, 255, 255, 0.6);
  flex-shrink: 0;
}
</style>
