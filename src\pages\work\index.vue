<template>
  <view class="page-container">
    <!-- 头部区域 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content safe-area-inset-top">
        <text class="page-title">工作工具</text>
        <text class="page-desc">提升工作效率的实用工具集</text>
      </view>
    </view>
    
    <!-- 工具列表 -->
    <view class="tools-section">
      <view class="tools-grid">
        <view 
          v-for="tool in tools" 
          :key="tool.id"
          class="tool-card"
          @tap="goToTool(tool)"
        >
          <view class="tool-bg" :style="{ background: tool.gradient }"></view>
          <view class="tool-content">
            <view class="tool-icon">{{ tool.icon }}</view>
            <text class="tool-name">{{ tool.name }}</text>
            <text class="tool-desc">{{ tool.desc }}</text>
            <view class="tool-features">
              <text 
                v-for="feature in tool.features" 
                :key="feature"
                class="feature-tag"
              >
                {{ feature }}
              </text>
            </view>
          </view>
          <view class="tool-status" v-if="tool.status">
            <text class="status-text">{{ tool.status }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 使用统计 -->
    <view class="stats-section">
      <view class="section-title">
        <text class="title-text">使用统计</text>
        <text class="title-desc">您的工具使用情况</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-card card">
          <view class="stat-icon">🔧</view>
          <view class="stat-info">
            <text class="stat-value">{{ toolStats.totalUsage }}</text>
            <text class="stat-label">总使用次数</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">📅</view>
          <view class="stat-info">
            <text class="stat-value">{{ toolStats.todayUsage }}</text>
            <text class="stat-label">今日使用</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">⭐</view>
          <view class="stat-info">
            <text class="stat-value">{{ toolStats.favoriteCount }}</text>
            <text class="stat-label">收藏工具</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">⏱️</view>
          <view class="stat-info">
            <text class="stat-value">{{ formatTime(toolStats.totalTime) }}</text>
            <text class="stat-label">使用时长</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 最近使用 -->
    <view class="recent-section" v-if="recentTools.length > 0">
      <view class="section-title">
        <text class="title-text">最近使用</text>
        <text class="title-desc">快速访问常用工具</text>
      </view>
      
      <view class="recent-list">
        <view 
          v-for="tool in recentTools" 
          :key="tool.id"
          class="recent-item card"
          @tap="goToTool(tool)"
        >
          <view class="tool-info">
            <view class="tool-icon-small">{{ tool.icon }}</view>
            <view class="tool-details">
              <text class="tool-name">{{ tool.name }}</text>
              <text class="tool-time">{{ formatDateTime(tool.lastUsed) }}</text>
            </view>
          </view>
          <view class="tool-arrow">></view>
        </view>
      </view>
    </view>
    
    <!-- 工具分类 -->
    <view class="categories-section">
      <view class="section-title">
        <text class="title-text">工具分类</text>
        <text class="title-desc">按类别浏览工具</text>
      </view>
      
      <view class="categories-list">
        <view 
          v-for="category in categories" 
          :key="category.id"
          class="category-item card"
          @tap="showCategory(category)"
        >
          <view class="category-icon">{{ category.icon }}</view>
          <view class="category-info">
            <text class="category-name">{{ category.name }}</text>
            <text class="category-desc">{{ category.desc }}</text>
            <text class="category-count">{{ category.count }}个工具</text>
          </view>
          <view class="category-arrow">></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRedPacketStore } from '../../stores/redpacket.js'
import audioManager from '../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 工具配置
const tools = ref([
  {
    id: 'idcard',
    name: '证件号生成器',
    desc: '生成身份证号码',
    icon: '🆔',
    path: '/pages/work/idcard/index',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    features: ['批量生成', '格式验证', '地区选择'],
    status: '完整版',
    category: 'generator'
  },
  {
    id: 'watermark',
    name: '去水印工具',
    desc: '图片视频去水印',
    icon: '🖼️',
    path: '/pages/work/watermark/index',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    features: ['图片处理', '视频处理'],
    status: '开发中',
    category: 'media'
  },
  {
    id: 'qrcode',
    name: '二维码生成器',
    desc: '生成各种二维码',
    icon: '📱',
    path: '/pages/work/qrcode/index',
    gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    features: ['文本转码', '链接转码'],
    status: '规划中',
    category: 'generator'
  },
  {
    id: 'password',
    name: '密码生成器',
    desc: '生成安全密码',
    icon: '🔐',
    path: '/pages/work/password/index',
    gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    features: ['自定义规则', '强度检测'],
    status: '规划中',
    category: 'security'
  }
])

// 工具分类
const categories = ref([
  {
    id: 'generator',
    name: '生成器工具',
    desc: '各种数据生成工具',
    icon: '⚙️',
    count: 2
  },
  {
    id: 'media',
    name: '媒体处理',
    desc: '图片视频处理工具',
    icon: '🎨',
    count: 1
  },
  {
    id: 'security',
    name: '安全工具',
    desc: '密码安全相关工具',
    icon: '🔒',
    count: 1
  }
])

// 工具统计数据
const toolStats = ref({
  totalUsage: 0,
  todayUsage: 0,
  favoriteCount: 0,
  totalTime: 0
})

// 最近使用的工具
const recentTools = ref([])

// 页面加载
onMounted(() => {
  loadToolStats()
  loadRecentTools()
  audioManager.playSound('page_turn')
})

// 加载工具统计
const loadToolStats = () => {
  try {
    const stats = uni.getStorageSync('tool_stats')
    if (stats) {
      toolStats.value = { ...toolStats.value, ...stats }
    }
  } catch (error) {
    console.warn('加载工具统计失败:', error)
  }
}

// 加载最近使用工具
const loadRecentTools = () => {
  try {
    const recent = uni.getStorageSync('recent_tools')
    if (recent && Array.isArray(recent)) {
      // 匹配工具信息
      recentTools.value = recent.map(recentTool => {
        const tool = tools.value.find(t => t.id === recentTool.id)
        return tool ? { ...tool, lastUsed: recentTool.lastUsed } : null
      }).filter(Boolean).slice(0, 3) // 只显示最近3个
    }
  } catch (error) {
    console.warn('加载最近工具失败:', error)
  }
}

// 跳转到工具
const goToTool = (tool) => {
  audioManager.playSound('button_click')
  
  if (tool.status === '开发中' || tool.status === '规划中') {
    uni.showToast({
      title: '功能开发中，敬请期待',
      icon: 'none'
    })
    return
  }
  
  // 触发使用工具统计
  redPacketStore.onToolUsed()
  
  // 记录使用
  recordToolUsage(tool)
  
  uni.navigateTo({
    url: tool.path
  })
}

// 显示分类
const showCategory = (category) => {
  audioManager.playSound('click')
  
  const categoryTools = tools.value.filter(tool => tool.category === category.id)
  
  uni.showActionSheet({
    itemList: categoryTools.map(tool => `${tool.icon} ${tool.name}`),
    success: (res) => {
      const selectedTool = categoryTools[res.tapIndex]
      if (selectedTool) {
        goToTool(selectedTool)
      }
    }
  })
}

// 记录工具使用
const recordToolUsage = (tool) => {
  try {
    // 更新统计
    const stats = uni.getStorageSync('tool_stats') || {
      totalUsage: 0,
      todayUsage: 0,
      favoriteCount: 0,
      totalTime: 0
    }
    
    stats.totalUsage += 1
    stats.todayUsage += 1
    
    uni.setStorageSync('tool_stats', stats)
    
    // 更新最近使用
    const recent = uni.getStorageSync('recent_tools') || []
    const existingIndex = recent.findIndex(item => item.id === tool.id)
    
    const recentItem = {
      id: tool.id,
      lastUsed: new Date().toISOString()
    }
    
    if (existingIndex >= 0) {
      recent.splice(existingIndex, 1)
    }
    
    recent.unshift(recentItem)
    
    // 只保留最近10个
    if (recent.length > 10) {
      recent.splice(10)
    }
    
    uni.setStorageSync('recent_tools', recent)
    
    // 重新加载数据
    loadToolStats()
    loadRecentTools()
  } catch (error) {
    console.warn('记录工具使用失败:', error)
  }
}

// 格式化时间
const formatTime = (seconds) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
  return `${Math.floor(seconds / 3600)}小时`
}

// 格式化日期时间
const formatDateTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 86400000) { // 24小时内
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  } else {
    return date.toLocaleDateString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit' 
    })
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

.header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.header-content {
  position: relative;
  height: 100%;
  padding: 40rpx $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  font-size: $font-size-md;
  opacity: 0.9;
}

.tools-section, .stats-section, .recent-section, .categories-section {
  padding: $spacing-md;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
  margin-top: -60rpx;
  position: relative;
  z-index: 1;
}

.tool-card {
  position: relative;
  height: 280rpx;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-lg;
}

.tool-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.tool-content {
  position: relative;
  height: 100%;
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.tool-icon {
  font-size: 80rpx;
  margin-bottom: $spacing-sm;
}

.tool-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  margin-bottom: 5rpx;
  display: block;
}

.tool-desc {
  font-size: $font-size-sm;
  opacity: 0.9;
  margin-bottom: $spacing-sm;
  display: block;
}

.tool-features {
  display: flex;
  flex-wrap: wrap;
  gap: 5rpx;
  justify-content: center;
}

.feature-tag {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  backdrop-filter: blur(5px);
}

.tool-status {
  position: absolute;
  top: $spacing-sm;
  right: $spacing-sm;
  padding: 4rpx 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  backdrop-filter: blur(5px);
}

.status-text {
  font-size: 20rpx;
  color: white;
  font-weight: $font-weight-bold;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.title-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  gap: $spacing-md;
}

.stat-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.recent-list, .categories-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.recent-item, .category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
}

.tool-info, .category-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex: 1;
}

.tool-icon-small, .category-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.tool-details {
  flex: 1;
}

.tool-name, .category-name {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.tool-time, .category-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.category-count {
  font-size: $font-size-xs;
  color: $text-color-light;
  display: block;
  margin-top: 2rpx;
}

.tool-arrow, .category-arrow {
  font-size: $font-size-lg;
  color: $text-color-light;
}
</style>
