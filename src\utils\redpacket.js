/**
 * 红包工具类
 * 提供红包相关的工具函数和业务逻辑
 */

import audioManager from './audio.js'
import animationUtils from './animation.js'

class RedPacketUtils {
  constructor() {
    this.redPacketTypes = {
      newUser: '新用户红包',
      signIn: '签到红包',
      task: '任务红包',
      random: '惊喜红包',
      share: '分享红包',
      game: '游戏红包'
    }
    
    this.redPacketColors = {
      newUser: '#ff6b6b',
      signIn: '#4ecdc4',
      task: '#45b7d1',
      random: '#f9ca24',
      share: '#6c5ce7',
      game: '#fd79a8'
    }
  }
  
  /**
   * 格式化金额显示
   */
  formatAmount(amount) {
    if (amount >= 1) {
      return amount.toFixed(2)
    } else {
      return amount.toFixed(1)
    }
  }
  
  /**
   * 格式化红包标题
   */
  formatTitle(type, extra = {}) {
    switch (type) {
      case 'newUser':
        return '🎉 新用户红包'
      case 'signIn':
        return `📅 签到第${extra.days || 1}天`
      case 'task':
        return '🎯 任务完成红包'
      case 'random':
        return '🎁 惊喜红包'
      case 'share':
        return '📤 分享红包'
      case 'game':
        return '🎮 游戏红包'
      default:
        return '🧧 红包'
    }
  }
  
  /**
   * 格式化红包描述
   */
  formatDescription(type, extra = {}) {
    switch (type) {
      case 'newUser':
        return '欢迎使用多功能工具箱！'
      case 'signIn':
        return `连续签到${extra.days || 1}天，获得红包奖励！`
      case 'task':
        return `完成${extra.taskName || '任务'}，获得红包奖励！`
      case 'random':
        return '恭喜你获得惊喜红包！'
      case 'share':
        return '感谢分享，获得红包奖励！'
      case 'game':
        return `${extra.gameName || '游戏'}获胜，获得红包奖励！`
      default:
        return '恭喜获得红包！'
    }
  }
  
  /**
   * 获取红包颜色
   */
  getRedPacketColor(type) {
    return this.redPacketColors[type] || '#ff6b6b'
  }
  
  /**
   * 生成红包ID
   */
  generateRedPacketId() {
    return `rp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 计算签到红包金额
   */
  calculateSignInAmount(days, config) {
    const baseAmount = config.baseAmount || 1
    const incrementAmount = config.incrementAmount || 0.5
    const maxAmount = config.maxAmount || 10
    
    const amount = baseAmount + ((days - 1) * incrementAmount)
    return Math.min(amount, maxAmount)
  }
  
  /**
   * 计算任务红包金额
   */
  calculateTaskAmount(taskType, config) {
    const taskConfig = config[taskType]
    if (!taskConfig || !taskConfig.amount) {
      return 1
    }
    
    const [min, max] = taskConfig.amount
    return Math.round((Math.random() * (max - min) + min) * 100) / 100
  }
  
  /**
   * 检查是否可以获得随机红包
   */
  checkRandomRedPacket(probability = 0.1) {
    return Math.random() < probability
  }
  
  /**
   * 播放红包音效
   */
  playRedPacketSound(action) {
    switch (action) {
      case 'appear':
        audioManager.playSound('notification')
        break
      case 'open':
        audioManager.playSound('redpacket_open')
        break
      case 'get':
        audioManager.playSound('redpacket_get')
        break
      case 'coin':
        audioManager.playSound('coin')
        break
      default:
        audioManager.playSound('notification')
    }
  }
  
  /**
   * 播放红包动画
   */
  async playRedPacketAnimation(selector, type = 'open') {
    switch (type) {
      case 'appear':
        await animationUtils.sequence([
          () => animationUtils.scaleIn(selector, { duration: 300 }),
          () => animationUtils.bounce(selector, { duration: 600 })
        ])
        break
        
      case 'open':
        await animationUtils.redPacketOpen(selector, { duration: 800 })
        break
        
      case 'coin':
        await animationUtils.coinFlyIn(selector, { duration: 1000 })
        break
        
      case 'disappear':
        await animationUtils.fadeOut(selector, { duration: 300 })
        break
        
      default:
        await animationUtils.bounce(selector, { duration: 600 })
    }
  }
  
  /**
   * 显示红包获得提示
   */
  showRedPacketTip(amount, type = 'success') {
    const title = type === 'success' ? '红包到账' : '红包提醒'
    const content = `恭喜获得 ¥${this.formatAmount(amount)} 红包！`
    
    uni.showToast({
      title: content,
      icon: type === 'success' ? 'success' : 'none',
      duration: 2000
    })
    
    // 播放音效
    this.playRedPacketSound('get')
  }
  
  /**
   * 分享红包
   */
  shareRedPacket(redPacket) {
    const shareContent = {
      title: `我在多功能工具箱获得了¥${this.formatAmount(redPacket.amount)}红包！`,
      desc: '快来一起使用这个超实用的工具箱吧！',
      path: '/pages/index/index',
      imageUrl: '/static/images/share_redpacket.png'
    }
    
    // #ifdef MP-WEIXIN
    uni.shareAppMessage(shareContent)
    // #endif
    
    // #ifdef H5
    if (navigator.share) {
      navigator.share({
        title: shareContent.title,
        text: shareContent.desc,
        url: window.location.href
      })
    } else {
      // 复制到剪贴板
      this.copyToClipboard(`${shareContent.title} ${shareContent.desc} ${window.location.href}`)
    }
    // #endif
  }
  
  /**
   * 复制到剪贴板
   */
  copyToClipboard(text) {
    // #ifdef MP-WEIXIN
    uni.setClipboardData({
      data: text,
      success: () => {
        uni.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
    // #endif
    
    // #ifdef H5
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text).then(() => {
        uni.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      })
    }
    // #endif
  }
  
  /**
   * 验证红包数据
   */
  validateRedPacket(redPacket) {
    if (!redPacket) return false
    
    const requiredFields = ['id', 'type', 'amount', 'timestamp']
    for (const field of requiredFields) {
      if (!redPacket[field]) {
        console.warn(`红包数据缺少必要字段: ${field}`)
        return false
      }
    }
    
    if (redPacket.amount <= 0) {
      console.warn('红包金额必须大于0')
      return false
    }
    
    if (!this.redPacketTypes[redPacket.type]) {
      console.warn(`未知的红包类型: ${redPacket.type}`)
      return false
    }
    
    return true
  }
  
  /**
   * 格式化时间显示
   */
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else if (diff < 2592000000) { // 30天内
      return `${Math.floor(diff / 86400000)}天前`
    } else {
      return date.toLocaleDateString()
    }
  }
  
  /**
   * 获取红包统计信息
   */
  getStatistics(redPacketHistory) {
    if (!redPacketHistory || redPacketHistory.length === 0) {
      return {
        totalCount: 0,
        totalAmount: 0,
        averageAmount: 0,
        maxAmount: 0,
        todayCount: 0,
        todayAmount: 0,
        typeStats: {}
      }
    }
    
    const today = new Date().toDateString()
    const todayPackets = redPacketHistory.filter(packet => 
      new Date(packet.timestamp).toDateString() === today
    )
    
    const totalAmount = redPacketHistory.reduce((sum, packet) => sum + packet.amount, 0)
    const todayAmount = todayPackets.reduce((sum, packet) => sum + packet.amount, 0)
    const maxAmount = Math.max(...redPacketHistory.map(packet => packet.amount))
    
    // 按类型统计
    const typeStats = {}
    redPacketHistory.forEach(packet => {
      if (!typeStats[packet.type]) {
        typeStats[packet.type] = { count: 0, amount: 0 }
      }
      typeStats[packet.type].count++
      typeStats[packet.type].amount += packet.amount
    })
    
    return {
      totalCount: redPacketHistory.length,
      totalAmount: totalAmount,
      averageAmount: totalAmount / redPacketHistory.length,
      maxAmount: maxAmount,
      todayCount: todayPackets.length,
      todayAmount: todayAmount,
      typeStats: typeStats
    }
  }
  
  /**
   * 导出红包记录
   */
  exportRedPacketHistory(redPacketHistory) {
    const csvContent = [
      ['时间', '类型', '金额', '描述', '状态'].join(','),
      ...redPacketHistory.map(packet => [
        new Date(packet.timestamp).toLocaleString(),
        this.redPacketTypes[packet.type] || packet.type,
        packet.amount,
        packet.description || '',
        packet.claimed ? '已领取' : '未领取'
      ].join(','))
    ].join('\n')
    
    return csvContent
  }
}

// 创建全局红包工具实例
const redPacketUtils = new RedPacketUtils()

export default redPacketUtils
