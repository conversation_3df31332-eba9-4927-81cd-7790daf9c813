/**
 * 动画工具类
 * 提供各种动画效果和工具函数
 */

class AnimationUtils {
  constructor() {
    this.animations = new Map()
    this.defaultDuration = 300
    this.defaultEasing = 'ease-out'
  }
  
  /**
   * 创建动画实例
   */
  createAnimation(options = {}) {
    const animation = uni.createAnimation({
      duration: options.duration || this.defaultDuration,
      timingFunction: options.easing || this.defaultEasing,
      delay: options.delay || 0,
      transformOrigin: options.transformOrigin || '50% 50% 0'
    })
    
    return animation
  }
  
  /**
   * 淡入动画
   */
  fadeIn(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.opacity(1).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 淡出动画
   */
  fadeOut(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.opacity(0).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 滑入动画（从下方）
   */
  slideInUp(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.translateY(0).opacity(1).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 滑出动画（向下）
   */
  slideOutDown(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.translateY('100%').opacity(0).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 滑入动画（从右方）
   */
  slideInRight(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.translateX(0).opacity(1).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 滑出动画（向右）
   */
  slideOutRight(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.translateX('100%').opacity(0).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 缩放进入动画
   */
  scaleIn(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.scale(1).opacity(1).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 缩放退出动画
   */
  scaleOut(selector, options = {}) {
    const animation = this.createAnimation(options)
    animation.scale(0).opacity(0).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 弹跳动画
   */
  bounce(selector, options = {}) {
    const animation = this.createAnimation({
      ...options,
      duration: options.duration || 600
    })
    
    animation.scale(1.2).step({ duration: 200 })
    animation.scale(0.9).step({ duration: 200 })
    animation.scale(1).step({ duration: 200 })
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 摇摆动画
   */
  shake(selector, options = {}) {
    const animation = this.createAnimation({
      ...options,
      duration: options.duration || 500
    })
    
    animation.translateX(10).step({ duration: 100 })
    animation.translateX(-10).step({ duration: 100 })
    animation.translateX(10).step({ duration: 100 })
    animation.translateX(-10).step({ duration: 100 })
    animation.translateX(0).step({ duration: 100 })
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 旋转动画
   */
  rotate(selector, options = {}) {
    const degrees = options.degrees || 360
    const animation = this.createAnimation(options)
    animation.rotate(degrees).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 脉冲动画
   */
  pulse(selector, options = {}) {
    const animation = this.createAnimation({
      ...options,
      duration: options.duration || 1000
    })
    
    animation.scale(1.1).step({ duration: 500 })
    animation.scale(1).step({ duration: 500 })
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 红包开启动画
   */
  redPacketOpen(selector, options = {}) {
    const animation = this.createAnimation({
      ...options,
      duration: options.duration || 800
    })
    
    // 第一阶段：轻微摇摆
    animation.rotate(5).step({ duration: 100 })
    animation.rotate(-5).step({ duration: 100 })
    animation.rotate(0).step({ duration: 100 })
    
    // 第二阶段：放大并淡出
    animation.scale(1.5).opacity(0.8).step({ duration: 200 })
    animation.scale(2).opacity(0).step({ duration: 300 })
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 金币飞入动画
   */
  coinFlyIn(selector, options = {}) {
    const animation = this.createAnimation({
      ...options,
      duration: options.duration || 1000
    })
    
    // 从右上角飞入
    animation.translateX(0).translateY(0).scale(1).opacity(1).step()
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 卡片翻转动画
   */
  cardFlip(selector, options = {}) {
    const animation = this.createAnimation({
      ...options,
      duration: options.duration || 600
    })
    
    animation.rotateY(90).step({ duration: 300 })
    animation.rotateY(0).step({ duration: 300 })
    
    return this.applyAnimation(selector, animation, options)
  }
  
  /**
   * 应用动画到元素
   */
  applyAnimation(selector, animation, options = {}) {
    return new Promise((resolve) => {
      const animationData = animation.export()
      
      // 如果是组件实例，直接设置动画
      if (typeof selector === 'object' && selector.setData) {
        selector.setData({
          [options.property || 'animationData']: animationData
        })
      }
      
      // 延迟执行回调
      setTimeout(() => {
        if (options.onComplete) {
          options.onComplete()
        }
        resolve()
      }, options.duration || this.defaultDuration)
    })
  }
  
  /**
   * 序列动画
   */
  sequence(animations) {
    return animations.reduce((promise, animationFn) => {
      return promise.then(() => animationFn())
    }, Promise.resolve())
  }
  
  /**
   * 并行动画
   */
  parallel(animations) {
    return Promise.all(animations.map(animationFn => animationFn()))
  }
  
  /**
   * 延迟执行
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * 缓动函数
   */
  easing = {
    linear: 'linear',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    
    // 自定义缓动
    bounceOut: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    backOut: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    elasticOut: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
  }
  
  /**
   * 预设动画组合
   */
  presets = {
    // 模态框进入
    modalEnter: (selector, options = {}) => {
      return this.sequence([
        () => this.fadeIn(selector, { duration: 200 }),
        () => this.scaleIn(selector, { duration: 300, easing: this.easing.backOut })
      ])
    },
    
    // 模态框退出
    modalExit: (selector, options = {}) => {
      return this.sequence([
        () => this.scaleOut(selector, { duration: 200 }),
        () => this.fadeOut(selector, { duration: 200 })
      ])
    },
    
    // 页面切换
    pageTransition: (outSelector, inSelector, options = {}) => {
      return this.parallel([
        () => this.slideOutRight(outSelector, { duration: 300 }),
        () => this.delay(150).then(() => this.slideInRight(inSelector, { duration: 300 }))
      ])
    },
    
    // 成功提示
    successTip: (selector, options = {}) => {
      return this.sequence([
        () => this.scaleIn(selector, { duration: 200 }),
        () => this.pulse(selector, { duration: 400 }),
        () => this.delay(1000),
        () => this.fadeOut(selector, { duration: 300 })
      ])
    },
    
    // 错误提示
    errorTip: (selector, options = {}) => {
      return this.sequence([
        () => this.scaleIn(selector, { duration: 200 }),
        () => this.shake(selector, { duration: 500 }),
        () => this.delay(1000),
        () => this.fadeOut(selector, { duration: 300 })
      ])
    }
  }
  
  /**
   * 清理动画
   */
  clear(selector) {
    if (this.animations.has(selector)) {
      this.animations.delete(selector)
    }
  }
  
  /**
   * 清理所有动画
   */
  clearAll() {
    this.animations.clear()
  }
}

// 创建全局动画工具实例
const animationUtils = new AnimationUtils()

export default animationUtils
