<template>
  <view class="chess-container">
    <!-- 游戏头部 -->
    <view class="game-header">
      <view class="player-info">
        <view class="player black" :class="{ active: currentPlayer === 'black' }">
          <text class="player-name">AI对手</text>
          <text class="player-status">{{ currentPlayer === 'black' ? '思考中...' : '等待中' }}</text>
        </view>
        
        <view class="game-status">
          <text class="status-text">{{ gameStatusText }}</text>
          <text class="move-count">第{{ moveCount }}回合</text>
        </view>
        
        <view class="player red" :class="{ active: currentPlayer === 'red' }">
          <text class="player-name">您</text>
          <text class="player-status">{{ currentPlayer === 'red' ? '请走棋' : '等待中' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 棋盘 -->
    <view class="chess-board-container">
      <canvas
        class="chess-board"
        canvas-id="chessBoard"
        @touchstart="onTouchStart"
        @touchend="onTouchEnd"
        :style="{
          width: (boardSize + 20) + 'px',
          height: (boardSize * 10/9 + 40) + 'px',
          margin: '10px auto',
          display: 'block'
        }"
      ></canvas>
      
      <!-- 走法提示 -->
      <view 
        v-if="selectedPiece"
        class="move-hints"
      >
        <view 
          v-for="(hint, index) in moveHints" 
          :key="index"
          class="move-hint"
          :style="{ 
            left: hint.x + 'px', 
            top: hint.y + 'px' 
          }"
        ></view>
      </view>
    </view>
    
    <!-- 游戏控制 -->
    <view class="game-controls">
      <button class="control-btn" @tap="undoMove" :disabled="!canUndo">
        <text class="btn-text">悔棋</text>
      </button>
      
      <button class="control-btn" @tap="restartGame">
        <text class="btn-text">重新开始</text>
      </button>
      
      <button class="control-btn" @tap="showSettings">
        <text class="btn-text">设置</text>
      </button>
      
      <button class="control-btn" @tap="exitGame">
        <text class="btn-text">退出</text>
      </button>
    </view>
    
    <!-- 走法记录 -->
    <view class="move-history" v-if="showHistory">
      <view class="history-header">
        <text class="history-title">走法记录</text>
        <button class="close-btn" @tap="showHistory = false">×</button>
      </view>
      <scroll-view class="history-list" scroll-y>
        <view 
          v-for="(move, index) in moveHistory" 
          :key="index"
          class="history-item"
        >
          <text class="move-number">{{ index + 1 }}.</text>
          <text class="move-desc">{{ move.description }}</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 游戏结束弹窗 -->
    <view v-if="gameOver" class="game-over-modal" @tap="closeGameOver">
      <view class="modal-content" @tap.stop>
        <text class="result-title">{{ gameResult.title }}</text>
        <text class="result-desc">{{ gameResult.desc }}</text>
        <view class="result-actions">
          <button class="action-btn primary" @tap="restartGame">再来一局</button>
          <button class="action-btn" @tap="exitGame">退出游戏</button>
        </view>
      </view>
    </view>
    
    <!-- 设置弹窗 -->
    <view v-if="showSettingsModal" class="settings-modal" @tap="closeSettings">
      <view class="modal-content" @tap.stop>
        <text class="modal-title">游戏设置</text>
        
        <view class="setting-item">
          <text class="setting-label">AI难度</text>
          <view class="difficulty-options">
            <button 
              v-for="level in difficultyLevels" 
              :key="level.key"
              class="difficulty-btn"
              :class="{ active: aiDifficulty === level.key }"
              @tap="setDifficulty(level.key)"
            >
              {{ level.label }}
            </button>
          </view>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">音效</text>
          <switch :checked="soundEnabled" @change="toggleSound" />
        </view>
        
        <view class="setting-item">
          <text class="setting-label">显示走法提示</text>
          <switch :checked="showMoveHints" @change="toggleMoveHints" />
        </view>
        
        <button class="close-settings-btn" @tap="closeSettings">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRedPacketStore } from '../../../stores/redpacket.js'
import ChessEngine from '../../../utils/chess-engine.js'
import audioManager from '../../../utils/audio.js'
import gameDebugger from '../../../utils/game-debug.js'

// Store
const redPacketStore = useRedPacketStore()

// 游戏引擎
let chessEngine = null
let canvasContext = null

// 响应式数据
const boardSize = ref(350)
const currentPlayer = ref('red')
const gameOver = ref(false)
const selectedPiece = ref(null)
const moveHints = ref([])
const showHistory = ref(false)
const showSettingsModal = ref(false)
const moveHistory = ref([])
const moveCount = ref(0)

// 游戏设置
const aiDifficulty = ref('medium')
const soundEnabled = ref(true)
const showMoveHints = ref(true)

const difficultyLevels = ref([
  { key: 'easy', label: '简单' },
  { key: 'medium', label: '中等' },
  { key: 'hard', label: '困难' }
])

// 计算属性
const canUndo = computed(() => moveHistory.value.length > 0 && !gameOver.value)

const gameStatusText = computed(() => {
  if (gameOver.value) {
    return chessEngine?.winner === 'red' ? '您获胜了！' : 'AI获胜了！'
  }
  return currentPlayer.value === 'red' ? '轮到您走棋' : 'AI思考中...'
})

const gameResult = computed(() => {
  if (!gameOver.value) return { title: '', desc: '' }
  
  const isWin = chessEngine?.winner === 'red'
  return {
    title: isWin ? '恭喜获胜！' : '很遗憾失败了',
    desc: isWin ? '您成功击败了AI对手！' : '继续努力，下次一定能赢！'
  }
})

// 页面加载
onMounted(() => {
  console.log('🎮 象棋游戏页面加载')

  // 运行调试检查
  try {
    const diagnosis = gameDebugger.quickDiagnosis()
    console.log('诊断结果:', diagnosis)
  } catch (error) {
    console.warn('调试器运行失败:', error)
  }

  initGame()
  loadSettings()
})

// 页面卸载
onUnmounted(() => {
  saveGameStats()
})

// 初始化游戏
const initGame = async () => {
  console.log('🎮 开始初始化象棋游戏')

  chessEngine = new ChessEngine()
  console.log('✅ 象棋引擎初始化完成')

  await nextTick()

  // 设置棋盘大小
  const systemInfo = uni.getSystemInfoSync()
  const screenWidth = systemInfo.screenWidth
  boardSize.value = Math.min(screenWidth - 40, 400)
  console.log('📐 棋盘大小设置为:', boardSize.value)

  // 延迟获取Canvas上下文，确保DOM已渲染
  setTimeout(() => {
    try {
      canvasContext = uni.createCanvasContext('chessBoard')
      console.log('🎨 Canvas上下文获取成功')

      // 再次延迟绘制，确保Canvas准备就绪
      setTimeout(() => {
        drawBoard()
        updateGameState()
        console.log('✅ 棋盘绘制完成')
      }, 100)
    } catch (error) {
      console.error('❌ Canvas初始化失败:', error)
    }
  }, 200)
}

// 调试点击位置
const debugClickPosition = ref(null)

// 绘制棋盘
const drawBoard = () => {
  if (!canvasContext) {
    console.warn('⚠️ Canvas上下文未初始化')
    return
  }

  console.log('🎨 开始绘制棋盘')

  const ctx = canvasContext
  const size = boardSize.value
  const cellSize = size / 9
  const boardHeight = size * 10/9 + 20
  const margin = 10 // 边距

  // 计算实际绘制区域，避免边界溢出
  const drawWidth = 8 * cellSize  // 8个格子的宽度
  const drawHeight = 9 * cellSize // 9个格子的高度

  console.log('📐 棋盘参数:', { size, cellSize, boardHeight, margin, drawWidth, drawHeight })

  // 清空画布
  ctx.clearRect(0, 0, drawWidth + margin * 2, drawHeight + margin * 2)

  // 绘制木质背景纹理
  drawWoodBackground(ctx, drawWidth + margin * 2, drawHeight + margin * 2)

  // 绘制网格线
  ctx.strokeStyle = '#654321'
  ctx.lineWidth = 1.5

  // 竖线
  for (let i = 0; i <= 8; i++) {
    ctx.beginPath()
    ctx.moveTo(i * cellSize + margin, margin)
    ctx.lineTo(i * cellSize + margin, drawHeight + margin)
    ctx.stroke()
  }

  // 横线 - 分段绘制，中间留出楚河汉界
  for (let i = 0; i <= 9; i++) {
    ctx.beginPath()
    if (i === 4 || i === 5) {
      // 楚河汉界处的线段
      ctx.moveTo(margin, i * cellSize + margin)
      ctx.lineTo(drawWidth * 0.2 + margin, i * cellSize + margin)
      ctx.moveTo(drawWidth * 0.8 + margin, i * cellSize + margin)
      ctx.lineTo(drawWidth + margin, i * cellSize + margin)
    } else {
      ctx.moveTo(margin, i * cellSize + margin)
      ctx.lineTo(drawWidth + margin, i * cellSize + margin)
    }
    ctx.stroke()
  }

  // 绘制楚河汉界文字
  drawRiverText(ctx, drawWidth, cellSize, margin)

  // 绘制九宫格斜线
  drawPalaceLines(ctx, cellSize, margin)

  // 绘制兵线标记
  drawPawnMarks(ctx, cellSize, margin)

  // 绘制棋子
  drawPieces()

  // 绘制调试点击位置
  if (debugClickPosition.value) {
    drawDebugClick(ctx, debugClickPosition.value)
  }

  console.log('🎨 棋盘绘制完成，调用ctx.draw()')
  ctx.draw()
}

// 绘制调试点击位置
const drawDebugClick = (ctx, position) => {
  const { x, y, col, row } = position

  // 绘制点击位置的红色圆点
  ctx.beginPath()
  ctx.arc(x, y, 5, 0, 2 * Math.PI)
  ctx.fillStyle = '#FF0000'
  ctx.fill()

  // 绘制计算出的格子位置的绿色圆圈
  const cellSize = boardSize.value / 9
  const margin = 10
  const gridX = col * cellSize + margin
  const gridY = row * cellSize + margin

  ctx.beginPath()
  ctx.arc(gridX, gridY, 8, 0, 2 * Math.PI)
  ctx.strokeStyle = '#00FF00'
  ctx.lineWidth = 2
  ctx.stroke()
}

// 绘制木质背景
const drawWoodBackground = (ctx, width, height) => {
  // 基础木色背景
  ctx.fillStyle = '#DEB887'
  ctx.fillRect(0, 0, width, height)

  // 添加简单的木纹效果
  ctx.strokeStyle = 'rgba(139, 69, 19, 0.1)'
  ctx.lineWidth = 0.5
  for (let i = 0; i < 15; i++) {
    ctx.beginPath()
    ctx.moveTo(0, i * height / 15)
    ctx.lineTo(width, i * height / 15)
    ctx.stroke()
  }
}

// 绘制楚河汉界文字
const drawRiverText = (ctx, size, cellSize, margin) => {
  ctx.font = 'bold 18px serif'
  ctx.fillStyle = '#8B4513'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  const riverY = 4.5 * cellSize + margin

  // 楚河
  ctx.fillText('楚', size * 0.3, riverY - 8)
  ctx.fillText('河', size * 0.3, riverY + 8)

  // 汉界
  ctx.fillText('汉', size * 0.7, riverY - 8)
  ctx.fillText('界', size * 0.7, riverY + 8)
}

// 绘制九宫格斜线
const drawPalaceLines = (ctx, cellSize, margin) => {
  ctx.strokeStyle = '#654321'
  ctx.lineWidth = 1.5

  // 上方九宫格（黑方）
  ctx.beginPath()
  ctx.moveTo(3 * cellSize + margin, margin)
  ctx.lineTo(5 * cellSize + margin, 2 * cellSize + margin)
  ctx.stroke()

  ctx.beginPath()
  ctx.moveTo(5 * cellSize + margin, margin)
  ctx.lineTo(3 * cellSize + margin, 2 * cellSize + margin)
  ctx.stroke()

  // 下方九宫格（红方）
  ctx.beginPath()
  ctx.moveTo(3 * cellSize + margin, 7 * cellSize + margin)
  ctx.lineTo(5 * cellSize + margin, 9 * cellSize + margin)
  ctx.stroke()

  ctx.beginPath()
  ctx.moveTo(5 * cellSize + margin, 7 * cellSize + margin)
  ctx.lineTo(3 * cellSize + margin, 9 * cellSize + margin)
  ctx.stroke()
}

// 绘制兵线标记点
const drawPawnMarks = (ctx, cellSize, margin) => {
  ctx.fillStyle = '#654321'
  const markSize = 2

  // 兵线位置标记
  const pawnPositions = [
    // 黑方兵线
    [0, 3], [2, 3], [4, 3], [6, 3], [8, 3],
    // 红方兵线
    [0, 6], [2, 6], [4, 6], [6, 6], [8, 6],
    // 炮位标记
    [1, 2], [7, 2], [1, 7], [7, 7]
  ]

  pawnPositions.forEach(([col, row]) => {
    const x = col * cellSize + margin
    const y = row * cellSize + margin

    // 绘制小圆点标记
    ctx.beginPath()
    ctx.arc(x, y, markSize, 0, 2 * Math.PI)
    ctx.fill()
  })
}

// 绘制棋子
const drawPieces = () => {
  if (!chessEngine || !canvasContext) return

  const ctx = canvasContext
  const cellSize = boardSize.value / 9
  const pieceRadius = cellSize * 0.35
  const margin = 10

  for (let row = 0; row < 10; row++) {
    for (let col = 0; col < 9; col++) {
      const piece = chessEngine.board[row][col]
      if (piece === '.') continue

      const x = col * cellSize + margin
      const y = row * cellSize + margin

      // 检查是否为选中棋子
      const isSelected = selectedPiece.value &&
                        selectedPiece.value.row === row &&
                        selectedPiece.value.col === col

      // 绘制选中高亮效果
      if (isSelected) {
        drawSelectedGlow(ctx, x, y, pieceRadius)
      }

      // 绘制棋子立体效果
      drawPieceWithShadow(ctx, x, y, pieceRadius, piece, isSelected)
    }
  }
}

// 绘制选中棋子的光晕效果
const drawSelectedGlow = (ctx, x, y, radius) => {
  // 简单的金色光圈
  ctx.beginPath()
  ctx.arc(x, y, radius + 6, 0, 2 * Math.PI)
  ctx.strokeStyle = '#FFD700'
  ctx.lineWidth = 4
  ctx.stroke()

  ctx.beginPath()
  ctx.arc(x, y, radius + 3, 0, 2 * Math.PI)
  ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)'
  ctx.lineWidth = 2
  ctx.stroke()
}

// 绘制带阴影的立体棋子
const drawPieceWithShadow = (ctx, x, y, radius, piece, isSelected) => {
  const isRed = piece >= 'A' && piece <= 'Z'

  // 绘制阴影
  ctx.beginPath()
  ctx.arc(x + 2, y + 2, radius, 0, 2 * Math.PI)
  ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
  ctx.fill()

  // 绘制棋子主体
  ctx.beginPath()
  ctx.arc(x, y, radius, 0, 2 * Math.PI)
  ctx.fillStyle = isRed ? '#F44336' : '#2196F3'
  ctx.fill()

  // 棋子边框
  ctx.beginPath()
  ctx.arc(x, y, radius, 0, 2 * Math.PI)
  ctx.strokeStyle = isSelected ? '#FFD700' : '#333'
  ctx.lineWidth = isSelected ? 3 : 2
  ctx.stroke()

  // 内圈装饰
  ctx.beginPath()
  ctx.arc(x, y, radius - 3, 0, 2 * Math.PI)
  ctx.strokeStyle = isRed ? '#FFCDD2' : '#BBDEFB'
  ctx.lineWidth = 1
  ctx.stroke()

  // 绘制棋子文字
  ctx.fillStyle = '#FFF'
  ctx.font = `bold ${Math.floor(radius * 1.2)}px Arial`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(chessEngine.getPieceName(piece), x, y)
}

// 触摸开始
const onTouchStart = (e) => {
  if (currentPlayer.value !== 'red' || gameOver.value) return

  try {
    const touch = e.touches[0]
    console.log('🖱️ 触摸坐标:', { x: touch.clientX, y: touch.clientY })

    // 获取Canvas元素的位置信息
    const query = uni.createSelectorQuery()
    query.select('.chess-board').boundingClientRect((rect) => {
      if (!rect) {
        console.warn('⚠️ 无法获取Canvas位置信息')
        return
      }

      console.log('📐 Canvas位置:', rect)

      // 计算相对于Canvas的坐标
      const x = touch.clientX - rect.left
      const y = touch.clientY - rect.top

      console.log('📍 Canvas内坐标:', { x, y })

      const cellSize = boardSize.value / 9
      const margin = 10

      // 计算棋盘的有效区域
      const boardWidth = 8 * cellSize + margin * 2   // 棋盘宽度
      const boardHeight = 9 * cellSize + margin * 2  // 棋盘高度

      console.log('📏 棋盘区域:', { boardWidth, boardHeight, cellSize, margin })

      // 检查点击是否在棋盘有效区域内
      if (x < margin || x > boardWidth - margin || y < margin || y > boardHeight - margin) {
        console.log('❌ 点击在棋盘区域外:', { x, y, boardWidth, boardHeight })
        return
      }

      // 计算棋盘格子坐标 - 使用更精确的计算
      const relativeX = x - margin
      const relativeY = y - margin

      // 找到最近的交叉点
      const col = Math.round(relativeX / cellSize)
      const row = Math.round(relativeY / cellSize)

      // 检查点击是否在棋子附近（增加容错范围）
      const pieceX = col * cellSize + margin
      const pieceY = row * cellSize + margin
      const distance = Math.sqrt((x - pieceX) ** 2 + (y - pieceY) ** 2)
      const pieceRadius = cellSize * 0.35

      console.log('🎯 精确计算:', {
        relativeX, relativeY,
        pieceX, pieceY,
        distance, pieceRadius,
        isNearPiece: distance <= pieceRadius * 1.5
      })

      console.log('🎯 计算的格子坐标:', { col, row, cellSize, margin })

      // 记录调试信息并重绘
      debugClickPosition.value = { x, y, col, row }
      drawBoard()

      // 边界检查
      if (col < 0 || col > 8 || row < 0 || row > 9) {
        console.log('❌ 点击超出棋盘范围:', { col, row })
        // 清除调试信息
        setTimeout(() => {
          debugClickPosition.value = null
          drawBoard()
        }, 2000)
        return
      }

      const piece = chessEngine.board[row][col]
      console.log('♟️ 点击的棋子:', piece, '位置:', { row, col })

      // 选择棋子 - 必须是红方棋子
      if (piece !== '.' && chessEngine.isOwnPiece(piece, 'red')) {
        selectedPiece.value = { row, col }
        updateMoveHints()
        playSound('click')
        console.log('✅ 选中棋子:', { row, col, piece })

        // 清除调试信息
        setTimeout(() => {
          debugClickPosition.value = null
          drawBoard()
        }, 1000)
      }
      // 移动棋子 - 可以移动到空位或吃子
      else if (selectedPiece.value) {
        const move = {
          from: [selectedPiece.value.row, selectedPiece.value.col],
          to: [row, col]
        }

        console.log('🎯 尝试移动:', move)

        if (chessEngine.isValidMove(move)) {
          makeMove(move)
          console.log('✅ 移动成功')
        } else {
          playSound('error')
          console.log('❌ 无效移动')
        }

        selectedPiece.value = null
        moveHints.value = []

        // 清除调试信息
        setTimeout(() => {
          debugClickPosition.value = null
          drawBoard()
        }, 1000)
      }
      // 点击空位但没有选中棋子
      else {
        console.log('ℹ️ 点击空位，无操作')

        // 清除调试信息
        setTimeout(() => {
          debugClickPosition.value = null
          drawBoard()
        }, 1000)
      }
    }).exec()
  } catch (error) {
    console.error('❌ 触摸事件处理失败:', error)
  }
}

// 触摸结束
const onTouchEnd = (e) => {
  // 处理触摸结束逻辑
}

// 更新走法提示
const updateMoveHints = () => {
  if (!selectedPiece.value || !showMoveHints.value) {
    moveHints.value = []
    return
  }

  const { row, col } = selectedPiece.value
  const piece = chessEngine.board[row][col]
  const moves = chessEngine.generatePieceMoves(row, col, piece.toLowerCase())

  const cellSize = boardSize.value / 9
  const margin = 10

  // 走法提示点应该显示在交叉点上，居中显示
  moveHints.value = moves.map(move => ({
    x: move.to[1] * cellSize + margin - 10,  // 提示点的中心位置 (20rpx/2 = 10rpx)
    y: move.to[0] * cellSize + margin - 10   // 提示点的中心位置 (20rpx/2 = 10rpx)
  }))

  console.log('🎯 走法提示更新:', moveHints.value)
}

// 执行走法
const makeMove = (move) => {
  if (!chessEngine.isValidMove(move)) return false

  const capturedPiece = chessEngine.board[move.to[0]][move.to[1]]

  // 执行走法
  chessEngine.makeMove(move)
  
  // 记录走法
  const moveDesc = chessEngine.getMoveDescription(move)
  moveHistory.value.push({
    move,
    description: moveDesc,
    player: currentPlayer.value
  })
  
  // 播放音效
  if (capturedPiece !== '.') {
    playSound('capture')
  } else {
    playSound('move')
  }
  
  // 更新游戏状态
  updateGameState()
  
  // 检查游戏结束
  if (chessEngine.gameOver) {
    handleGameOver()
    return true
  }
  
  // AI走棋
  if (chessEngine.currentPlayer === 'black') {
    setTimeout(() => {
      makeAIMove()
    }, 1000)
  }
  
  return true
}

// AI走棋
const makeAIMove = () => {
  if (chessEngine.currentPlayer !== 'black' || chessEngine.gameOver) return
  
  const aiMove = chessEngine.getAIMove(aiDifficulty.value)
  if (aiMove) {
    makeMove(aiMove)
  }
}

// 更新游戏状态
const updateGameState = () => {
  currentPlayer.value = chessEngine.currentPlayer
  gameOver.value = chessEngine.gameOver
  moveCount.value = Math.floor(chessEngine.moveHistory.length / 2) + 1
  
  drawBoard()
}

// 处理游戏结束
const handleGameOver = () => {
  const isWin = chessEngine.winner === 'red'
  
  // 播放结束音效
  playSound(isWin ? 'win' : 'lose')
  
  // 触发红包奖励
  if (isWin) {
    redPacketStore.onGamePlayed()
    // 可以添加游戏胜利红包
  }
  
  // 保存游戏统计
  saveGameStats(isWin)
}

// 悔棋
const undoMove = () => {
  if (!canUndo.value) return
  
  // 撤销玩家走法
  if (chessEngine.undoMove()) {
    moveHistory.value.pop()
    
    // 如果上一步是AI走法，也要撤销
    if (chessEngine.currentPlayer === 'red' && chessEngine.moveHistory.length > 0) {
      chessEngine.undoMove()
      moveHistory.value.pop()
    }
    
    updateGameState()
    playSound('click')
  }
}

// 重新开始游戏
const restartGame = () => {
  chessEngine.reset()
  selectedPiece.value = null
  moveHints.value = []
  moveHistory.value = []
  gameOver.value = false
  
  updateGameState()
  playSound('click')
}

// 退出游戏
const exitGame = () => {
  uni.navigateBack()
}

// 显示设置
const showSettings = () => {
  showSettingsModal.value = true
  playSound('click')
}

// 关闭设置
const closeSettings = () => {
  showSettingsModal.value = false
  saveSettings()
}

// 关闭游戏结束弹窗
const closeGameOver = () => {
  // 点击遮罩不关闭，需要选择操作
}

// 设置AI难度
const setDifficulty = (level) => {
  aiDifficulty.value = level
  playSound('click')
}

// 切换音效
const toggleSound = (e) => {
  soundEnabled.value = e.detail.value
}

// 切换走法提示
const toggleMoveHints = (e) => {
  showMoveHints.value = e.detail.value
  if (!e.detail.value) {
    moveHints.value = []
  }
}

// 播放音效
const playSound = (soundName) => {
  if (soundEnabled.value) {
    audioManager.playSound(soundName)
  }
}

// 加载设置
const loadSettings = () => {
  try {
    const settings = uni.getStorageSync('chess_settings')
    if (settings) {
      aiDifficulty.value = settings.aiDifficulty || 'medium'
      soundEnabled.value = settings.soundEnabled !== false
      showMoveHints.value = settings.showMoveHints !== false
    }
  } catch (error) {
    console.warn('加载设置失败:', error)
  }
}

// 保存设置
const saveSettings = () => {
  try {
    const settings = {
      aiDifficulty: aiDifficulty.value,
      soundEnabled: soundEnabled.value,
      showMoveHints: showMoveHints.value
    }
    uni.setStorageSync('chess_settings', settings)
  } catch (error) {
    console.warn('保存设置失败:', error)
  }
}

// 保存游戏统计
const saveGameStats = (isWin = false) => {
  try {
    const stats = uni.getStorageSync('game_stats') || {
      totalGames: 0,
      winGames: 0,
      totalTime: 0
    }
    
    stats.totalGames += 1
    if (isWin) stats.winGames += 1
    
    uni.setStorageSync('game_stats', stats)
    
    // 保存最近游戏记录
    const recentGames = uni.getStorageSync('recent_games') || []
    recentGames.unshift({
      id: Date.now(),
      gameName: '中国象棋',
      result: isWin ? 'win' : 'lose',
      timestamp: new Date().toISOString()
    })
    
    // 只保留最近10局
    if (recentGames.length > 10) {
      recentGames.splice(10)
    }
    
    uni.setStorageSync('recent_games', recentGames)
  } catch (error) {
    console.warn('保存游戏统计失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.chess-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #8b5a2b 0%, #d2691e 100%);
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
}

.game-header {
  margin-bottom: $spacing-md;
}

.player-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  backdrop-filter: blur(10px);
}

.player {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  opacity: 0.6;
  transition: opacity $transition-normal;
  
  &.active {
    opacity: 1;
  }
}

.player-name {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  margin-bottom: 5rpx;
}

.player-status {
  font-size: $font-size-sm;
  opacity: 0.8;
}

.game-status {
  text-align: center;
  color: white;
}

.status-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  display: block;
  margin-bottom: 5rpx;
}

.move-count {
  font-size: $font-size-sm;
  opacity: 0.8;
}

.chess-board-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: $spacing-md;
  padding: 20rpx;
  overflow: visible;
}

.chess-board {
  background: linear-gradient(135deg, #DEB887 0%, #D2B48C 50%, #CD853F 100%);
  border-radius: $border-radius-md;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.3),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2),
    inset 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  border: 6rpx solid #8B4513;
  overflow: visible;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -3rpx;
    left: -3rpx;
    right: -3rpx;
    bottom: -3rpx;
    background: linear-gradient(45deg, #A0522D, #D2691E);
    border-radius: $border-radius-md;
    z-index: -1;
  }
}

.move-hints {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.move-hint {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: rgba(255, 215, 0, 0.9);
  border-radius: 50%;
  border: 3rpx solid #FFD700;
  box-shadow:
    0 0 10rpx rgba(255, 215, 0, 0.6),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.game-controls {
  display: flex;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;
}

.control-btn {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: $border-radius-md;
  color: white;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  backdrop-filter: blur(5px);
  
  &:disabled {
    opacity: 0.5;
  }
  
  &:active:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
  }
}

.move-history {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
  border-bottom: 1rpx solid $border-color-light;
}

.history-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background: $bg-color-light;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-xl;
  color: $text-color-secondary;
}

.history-list {
  max-height: 400rpx;
  padding: $spacing-md;
}

.history-item {
  display: flex;
  align-items: center;
  padding: $spacing-sm 0;
  gap: $spacing-sm;
}

.move-number {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  min-width: 60rpx;
}

.move-desc {
  font-size: $font-size-md;
  color: $text-color-primary;
}

.game-over-modal, .settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
}

.modal-content {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  max-width: 500rpx;
  width: 100%;
  text-align: center;
}

.result-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
  display: block;
}

.result-desc {
  font-size: $font-size-md;
  color: $text-color-secondary;
  margin-bottom: $spacing-lg;
  display: block;
}

.result-actions {
  display: flex;
  gap: $spacing-md;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  border: 2rpx solid $border-color-light;
  background: white;
  color: $text-color-primary;
  
  &.primary {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }
}

.modal-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  margin-bottom: $spacing-lg;
  display: block;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md 0;
  border-bottom: 1rpx solid $border-color-light;
  
  &:last-child {
    border-bottom: none;
  }
}

.setting-label {
  font-size: $font-size-md;
  color: $text-color-primary;
}

.difficulty-options {
  display: flex;
  gap: $spacing-sm;
}

.difficulty-btn {
  padding: $spacing-sm $spacing-md;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  background: white;
  color: $text-color-secondary;
  font-size: $font-size-sm;
  
  &.active {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }
}

.close-settings-btn {
  width: 100%;
  height: 80rpx;
  background: $primary-color;
  color: white;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  margin-top: $spacing-lg;
}
</style>
