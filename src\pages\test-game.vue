<template>
  <view class="test-container">
    <view class="header">
      <text class="title">游戏引擎测试</text>
    </view>
    
    <view class="test-section">
      <button class="test-btn" @tap="runTests">运行所有测试</button>
      <button class="test-btn" @tap="testChess">测试象棋引擎</button>
      <button class="test-btn" @tap="testGobang">测试五子棋引擎</button>
      <button class="test-btn" @tap="testCanvas">测试Canvas</button>
    </view>
    
    <view class="results-section">
      <text class="section-title">测试结果</text>
      <scroll-view class="results-list" scroll-y>
        <view 
          v-for="(result, index) in testResults" 
          :key="index"
          class="result-item"
          :class="{ passed: result.passed, failed: !result.passed }"
        >
          <text class="result-icon">{{ result.passed ? '✅' : '❌' }}</text>
          <view class="result-content">
            <text class="result-name">{{ result.name }}</text>
            <text class="result-message">{{ result.message }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <view class="summary-section" v-if="summary">
      <text class="summary-title">测试汇总</text>
      <view class="summary-stats">
        <view class="stat-item">
          <text class="stat-label">总计</text>
          <text class="stat-value">{{ summary.total }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">通过</text>
          <text class="stat-value success">{{ summary.passed }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">失败</text>
          <text class="stat-value error">{{ summary.failed }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">成功率</text>
          <text class="stat-value">{{ summary.successRate }}%</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import gameDebugger from '../utils/game-debug.js'
import ChessEngine from '../utils/chess-engine.js'
import GobangEngine from '../utils/gobang-engine.js'

const testResults = ref([])
const summary = ref(null)

// 运行所有测试
const runTests = () => {
  console.log('开始运行所有测试...')
  testResults.value = []
  summary.value = null
  
  try {
    const results = gameDebugger.runAllTests()
    testResults.value = results
    
    const total = results.length
    const passed = results.filter(r => r.passed).length
    const failed = total - passed
    
    summary.value = {
      total,
      passed,
      failed,
      successRate: Math.round((passed / total) * 100)
    }
    
    uni.showToast({
      title: `测试完成: ${passed}/${total}`,
      icon: 'none'
    })
  } catch (error) {
    console.error('测试运行失败:', error)
    uni.showToast({
      title: '测试运行失败',
      icon: 'error'
    })
  }
}

// 测试象棋引擎
const testChess = () => {
  console.log('测试象棋引擎...')
  testResults.value = []
  
  try {
    const chess = new ChessEngine()
    
    addTestResult('象棋引擎初始化', chess.board !== null, '棋盘初始化成功')
    
    const piece = chess.board[9][4]
    addTestResult('棋子识别', piece === 'K', `红帅位置: ${piece}`)
    
    const moves = chess.generateMoves('red')
    addTestResult('走法生成', moves.length > 0, `生成${moves.length}个走法`)
    
    const testMove = { from: [9, 4], to: [8, 4] }
    const isValid = chess.isValidMove(testMove)
    addTestResult('走法验证', isValid, '帅可以向前移动')
    
    uni.showToast({
      title: '象棋引擎测试完成',
      icon: 'success'
    })
  } catch (error) {
    addTestResult('象棋引擎', false, `错误: ${error.message}`)
    uni.showToast({
      title: '象棋引擎测试失败',
      icon: 'error'
    })
  }
}

// 测试五子棋引擎
const testGobang = () => {
  console.log('测试五子棋引擎...')
  testResults.value = []
  
  try {
    const gobang = new GobangEngine()
    
    addTestResult('五子棋引擎初始化', gobang.board !== null, '棋盘初始化成功')
    
    addTestResult('棋盘大小', gobang.board.length === 15, `棋盘大小: ${gobang.board.length}x${gobang.board[0].length}`)
    
    const moveResult = gobang.makeMove(7, 7, 1)
    addTestResult('落子功能', moveResult, '中心位置落子成功')
    
    const hasWin = gobang.checkWin(7, 7, 1)
    addTestResult('胜负判断', !hasWin, '单子不构成胜利')
    
    uni.showToast({
      title: '五子棋引擎测试完成',
      icon: 'success'
    })
  } catch (error) {
    addTestResult('五子棋引擎', false, `错误: ${error.message}`)
    uni.showToast({
      title: '五子棋引擎测试失败',
      icon: 'error'
    })
  }
}

// 测试Canvas
const testCanvas = () => {
  console.log('测试Canvas...')
  testResults.value = []
  
  try {
    const canvasContext = uni.createCanvasContext('test-canvas')
    addTestResult('Canvas支持', canvasContext !== null, 'Canvas上下文创建成功')
    
    if (canvasContext) {
      canvasContext.fillStyle = '#FF0000'
      canvasContext.fillRect(0, 0, 10, 10)
      addTestResult('Canvas绘制', true, '基本绘制功能正常')
    }
    
    uni.showToast({
      title: 'Canvas测试完成',
      icon: 'success'
    })
  } catch (error) {
    addTestResult('Canvas支持', false, `Canvas错误: ${error.message}`)
    uni.showToast({
      title: 'Canvas测试失败',
      icon: 'error'
    })
  }
}

// 添加测试结果
const addTestResult = (name, passed, message) => {
  testResults.value.push({
    name,
    passed,
    message,
    timestamp: new Date().toISOString()
  })
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx;
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-btn {
  height: 80rpx;
  background: #007aff;
  color: white;
  border-radius: 10rpx;
  font-size: 32rpx;
}

.results-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.results-list {
  height: 400rpx;
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
}

.result-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.passed {
    background: rgba(52, 199, 89, 0.1);
  }
  
  &.failed {
    background: rgba(255, 59, 48, 0.1);
  }
}

.result-icon {
  font-size: 32rpx;
  flex-shrink: 0;
}

.result-content {
  flex: 1;
}

.result-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.result-message {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.summary-section {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  
  &.success {
    color: #34c759;
  }
  
  &.error {
    color: #ff3b30;
  }
}
</style>
