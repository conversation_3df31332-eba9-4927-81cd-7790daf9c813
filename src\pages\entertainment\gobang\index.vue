<template>
  <view class="gobang-container">
    <!-- 游戏头部 -->
    <view class="game-header">
      <view class="player-info">
        <view class="player black" :class="{ active: currentPlayer === 1 }">
          <view class="player-avatar">⚫</view>
          <text class="player-name">您</text>
          <text class="player-status">{{ currentPlayer === 1 ? '请落子' : '等待中' }}</text>
        </view>
        
        <view class="game-status">
          <text class="status-text">{{ gameStatusText }}</text>
          <text class="move-count">第{{ moveCount }}手</text>
        </view>
        
        <view class="player white" :class="{ active: currentPlayer === 2 }">
          <view class="player-avatar">⚪</view>
          <text class="player-name">AI</text>
          <text class="player-status">{{ currentPlayer === 2 ? '思考中...' : '等待中' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 棋盘 -->
    <view class="board-container">
      <canvas 
        class="game-board" 
        canvas-id="gobangBoard"
        @touchstart="onTouchStart"
        :style="{ width: boardSize + 'px', height: boardSize + 'px' }"
      ></canvas>
      
      <!-- 最后落子标记 -->
      <view 
        v-if="lastMove"
        class="last-move-marker"
        :style="{ 
          left: lastMove.x + 'px', 
          top: lastMove.y + 'px' 
        }"
      ></view>
      
      <!-- 获胜连线 -->
      <view 
        v-if="winningLine && winningLine.length > 0"
        class="winning-line"
        :style="winningLineStyle"
      ></view>
    </view>
    
    <!-- 游戏控制 -->
    <view class="game-controls">
      <button class="control-btn" @tap="undoMove" :disabled="!canUndo">
        <text class="btn-icon">↶</text>
        <text class="btn-text">悔棋</text>
      </button>
      
      <button class="control-btn" @tap="restartGame">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重开</text>
      </button>
      
      <button class="control-btn" @tap="showSettings">
        <text class="btn-icon">⚙️</text>
        <text class="btn-text">设置</text>
      </button>
      
      <button class="control-btn" @tap="exitGame">
        <text class="btn-icon">🚪</text>
        <text class="btn-text">退出</text>
      </button>
    </view>
    
    <!-- 游戏结束弹窗 -->
    <view v-if="gameOver" class="game-over-modal" @tap="closeGameOver">
      <view class="modal-content" @tap.stop>
        <view class="result-icon">{{ gameResult.icon }}</view>
        <text class="result-title">{{ gameResult.title }}</text>
        <text class="result-desc">{{ gameResult.desc }}</text>
        <view class="game-stats">
          <text class="stats-text">用时: {{ formatTime(gameTime) }}</text>
          <text class="stats-text">手数: {{ moveCount }}</text>
        </view>
        <view class="result-actions">
          <button class="action-btn primary" @tap="restartGame">再来一局</button>
          <button class="action-btn" @tap="exitGame">退出游戏</button>
        </view>
      </view>
    </view>
    
    <!-- 设置弹窗 -->
    <view v-if="showSettingsModal" class="settings-modal" @tap="closeSettings">
      <view class="modal-content" @tap.stop>
        <text class="modal-title">游戏设置</text>
        
        <view class="setting-item">
          <text class="setting-label">AI难度</text>
          <view class="difficulty-options">
            <button 
              v-for="level in difficultyLevels" 
              :key="level.key"
              class="difficulty-btn"
              :class="{ active: aiDifficulty === level.key }"
              @tap="setDifficulty(level.key)"
            >
              {{ level.label }}
            </button>
          </view>
        </view>
        
        <view class="setting-item">
          <text class="setting-label">音效</text>
          <switch :checked="soundEnabled" @change="toggleSound" />
        </view>
        
        <view class="setting-item">
          <text class="setting-label">显示坐标</text>
          <switch :checked="showCoordinates" @change="toggleCoordinates" />
        </view>
        
        <view class="setting-item">
          <text class="setting-label">先手方</text>
          <view class="first-player-options">
            <button 
              class="first-player-btn"
              :class="{ active: playerFirst }"
              @tap="setFirstPlayer(true)"
            >
              玩家先手
            </button>
            <button 
              class="first-player-btn"
              :class="{ active: !playerFirst }"
              @tap="setFirstPlayer(false)"
            >
              AI先手
            </button>
          </view>
        </view>
        
        <button class="close-settings-btn" @tap="closeSettings">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRedPacketStore } from '../../../stores/redpacket.js'
import GobangEngine from '../../../utils/gobang-engine.js'
import audioManager from '../../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 游戏引擎
let gobangEngine = null
let canvasContext = null

// 响应式数据
const boardSize = ref(350)
const currentPlayer = ref(1) // 1: 黑子(玩家), 2: 白子(AI)
const gameOver = ref(false)
const lastMove = ref(null)
const winningLine = ref(null)
const showSettingsModal = ref(false)
const moveCount = ref(0)
const gameTime = ref(0)
const gameStartTime = ref(0)

// 游戏设置
const aiDifficulty = ref('medium')
const soundEnabled = ref(true)
const showCoordinates = ref(false)
const playerFirst = ref(true)

const difficultyLevels = ref([
  { key: 'easy', label: '简单' },
  { key: 'medium', label: '中等' },
  { key: 'hard', label: '困难' }
])

// 计算属性
const canUndo = computed(() => {
  return gobangEngine && gobangEngine.moveHistory.length > 0 && !gameOver.value
})

const gameStatusText = computed(() => {
  if (gameOver.value) {
    if (gobangEngine?.winner === 1) return '您获胜了！'
    if (gobangEngine?.winner === 2) return 'AI获胜了！'
    return '平局！'
  }
  return currentPlayer.value === 1 ? '轮到您落子' : 'AI思考中...'
})

const gameResult = computed(() => {
  if (!gameOver.value) return { icon: '', title: '', desc: '' }
  
  if (gobangEngine?.winner === 1) {
    return {
      icon: '🎉',
      title: '恭喜获胜！',
      desc: '您成功击败了AI对手！'
    }
  } else if (gobangEngine?.winner === 2) {
    return {
      icon: '😔',
      title: '很遗憾失败了',
      desc: '继续努力，下次一定能赢！'
    }
  } else {
    return {
      icon: '🤝',
      title: '平局',
      desc: '势均力敌的对局！'
    }
  }
})

const winningLineStyle = computed(() => {
  if (!winningLine.value || winningLine.value.length < 2) return {}
  
  const cellSize = boardSize.value / 14
  const start = winningLine.value[0]
  const end = winningLine.value[winningLine.value.length - 1]
  
  const x1 = start[1] * cellSize
  const y1 = start[0] * cellSize
  const x2 = end[1] * cellSize
  const y2 = end[0] * cellSize
  
  const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
  const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI
  
  return {
    left: x1 + 'px',
    top: y1 + 'px',
    width: length + 'px',
    transform: `rotate(${angle}deg)`,
    transformOrigin: '0 50%'
  }
})

// 页面加载
onMounted(() => {
  initGame()
  loadSettings()
})

// 页面卸载
onUnmounted(() => {
  saveGameStats()
})

// 初始化游戏
const initGame = async () => {
  gobangEngine = new GobangEngine()
  
  await nextTick()
  
  // 获取Canvas上下文
  canvasContext = uni.createCanvasContext('gobangBoard')
  
  // 设置棋盘大小
  const systemInfo = uni.getSystemInfoSync()
  const screenWidth = systemInfo.screenWidth
  boardSize.value = Math.min(screenWidth - 40, 400)
  
  // 开始游戏
  startGame()
}

// 开始游戏
const startGame = () => {
  gameStartTime.value = Date.now()
  
  // 绘制棋盘
  drawBoard()
  
  // 更新游戏状态
  updateGameState()
  
  // 如果AI先手
  if (!playerFirst.value) {
    setTimeout(() => {
      makeAIMove()
    }, 500)
  }
}

// 绘制棋盘
const drawBoard = () => {
  if (!canvasContext) return
  
  const ctx = canvasContext
  const size = boardSize.value
  const cellSize = size / 14 // 15x15棋盘，14个间隔
  const margin = cellSize / 2
  
  // 清空画布
  ctx.clearRect(0, 0, size, size)
  
  // 绘制背景
  ctx.fillStyle = '#DEB887'
  ctx.fillRect(0, 0, size, size)
  
  // 绘制网格线
  ctx.strokeStyle = '#8B4513'
  ctx.lineWidth = 1
  
  for (let i = 0; i < 15; i++) {
    const pos = margin + i * cellSize
    
    // 竖线
    ctx.beginPath()
    ctx.moveTo(pos, margin)
    ctx.lineTo(pos, size - margin)
    ctx.stroke()
    
    // 横线
    ctx.beginPath()
    ctx.moveTo(margin, pos)
    ctx.lineTo(size - margin, pos)
    ctx.stroke()
  }
  
  // 绘制星位
  const starPoints = [
    [3, 3], [3, 11], [7, 7], [11, 3], [11, 11]
  ]
  
  ctx.fillStyle = '#8B4513'
  starPoints.forEach(([row, col]) => {
    const x = margin + col * cellSize
    const y = margin + row * cellSize
    ctx.beginPath()
    ctx.arc(x, y, 3, 0, 2 * Math.PI)
    ctx.fill()
  })
  
  // 绘制坐标（如果开启）
  if (showCoordinates.value) {
    ctx.fillStyle = '#666'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    for (let i = 0; i < 15; i++) {
      const pos = margin + i * cellSize
      // 数字
      ctx.fillText((i + 1).toString(), 10, pos)
      // 字母
      ctx.fillText(String.fromCharCode(65 + i), pos, 10)
    }
  }
  
  // 绘制棋子
  drawPieces()
  
  ctx.draw()
}

// 绘制棋子
const drawPieces = () => {
  if (!gobangEngine || !canvasContext) return
  
  const ctx = canvasContext
  const cellSize = boardSize.value / 14
  const margin = cellSize / 2
  const pieceRadius = cellSize * 0.4
  
  for (let row = 0; row < 15; row++) {
    for (let col = 0; col < 15; col++) {
      const piece = gobangEngine.board[row][col]
      if (piece === 0) continue
      
      const x = margin + col * cellSize
      const y = margin + row * cellSize
      
      // 绘制棋子阴影
      ctx.beginPath()
      ctx.arc(x + 2, y + 2, pieceRadius, 0, 2 * Math.PI)
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
      ctx.fill()
      
      // 绘制棋子
      ctx.beginPath()
      ctx.arc(x, y, pieceRadius, 0, 2 * Math.PI)
      
      if (piece === 1) { // 黑子
        ctx.fillStyle = '#333'
        ctx.fill()
        ctx.strokeStyle = '#000'
        ctx.lineWidth = 1
        ctx.stroke()
      } else { // 白子
        ctx.fillStyle = '#FFF'
        ctx.fill()
        ctx.strokeStyle = '#333'
        ctx.lineWidth = 1
        ctx.stroke()
      }
    }
  }
}

// 触摸开始
const onTouchStart = (e) => {
  if (currentPlayer.value !== 1 || gameOver.value) return
  
  const touch = e.touches[0]
  const rect = e.currentTarget.getBoundingClientRect()
  const x = touch.clientX - rect.left
  const y = touch.clientY - rect.top
  
  const cellSize = boardSize.value / 14
  const margin = cellSize / 2
  
  const col = Math.round((x - margin) / cellSize)
  const row = Math.round((y - margin) / cellSize)
  
  if (row < 0 || row >= 15 || col < 0 || col >= 15) return
  
  // 尝试落子
  if (gobangEngine.makeMove(row, col, 1)) {
    // 记录最后落子位置
    lastMove.value = {
      x: margin + col * cellSize - 8,
      y: margin + row * cellSize - 8
    }
    
    playSound('move')
    updateGameState()
    
    // 检查游戏结束
    if (gobangEngine.gameOver) {
      handleGameOver()
      return
    }
    
    // AI走棋
    setTimeout(() => {
      makeAIMove()
    }, 500)
  } else {
    playSound('error')
  }
}

// AI走棋
const makeAIMove = () => {
  if (gobangEngine.currentPlayer !== 2 || gobangEngine.gameOver) return
  
  const aiMove = gobangEngine.getAIMove(aiDifficulty.value)
  if (aiMove) {
    const [row, col] = aiMove
    
    if (gobangEngine.makeMove(row, col, 2)) {
      // 记录最后落子位置
      const cellSize = boardSize.value / 14
      const margin = cellSize / 2
      
      lastMove.value = {
        x: margin + col * cellSize - 8,
        y: margin + row * cellSize - 8
      }
      
      playSound('move')
      updateGameState()
      
      // 检查游戏结束
      if (gobangEngine.gameOver) {
        handleGameOver()
      }
    }
  }
}

// 更新游戏状态
const updateGameState = () => {
  currentPlayer.value = gobangEngine.currentPlayer
  gameOver.value = gobangEngine.gameOver
  moveCount.value = gobangEngine.moveHistory.length
  winningLine.value = gobangEngine.winningLine
  
  if (!gameOver.value) {
    gameTime.value = Math.floor((Date.now() - gameStartTime.value) / 1000)
  }
  
  drawBoard()
}

// 处理游戏结束
const handleGameOver = () => {
  const isWin = gobangEngine.winner === 1
  
  // 播放结束音效
  playSound(isWin ? 'win' : 'lose')
  
  // 触发红包奖励
  if (isWin) {
    redPacketStore.onGamePlayed()
  }
  
  // 保存游戏统计
  saveGameStats(isWin)
}

// 悔棋
const undoMove = () => {
  if (!canUndo.value) return
  
  // 撤销玩家走法
  if (gobangEngine.undoMove()) {
    // 如果上一步是AI走法，也要撤销
    if (gobangEngine.currentPlayer === 1 && gobangEngine.moveHistory.length > 0) {
      gobangEngine.undoMove()
    }
    
    lastMove.value = null
    updateGameState()
    playSound('click')
  }
}

// 重新开始游戏
const restartGame = () => {
  gobangEngine.reset()
  lastMove.value = null
  winningLine.value = null
  gameOver.value = false
  gameStartTime.value = Date.now()
  gameTime.value = 0
  
  updateGameState()
  playSound('click')
  
  // 如果AI先手
  if (!playerFirst.value) {
    setTimeout(() => {
      makeAIMove()
    }, 500)
  }
}

// 退出游戏
const exitGame = () => {
  uni.navigateBack()
}

// 显示设置
const showSettings = () => {
  showSettingsModal.value = true
  playSound('click')
}

// 关闭设置
const closeSettings = () => {
  showSettingsModal.value = false
  saveSettings()
}

// 关闭游戏结束弹窗
const closeGameOver = () => {
  // 点击遮罩不关闭，需要选择操作
}

// 设置AI难度
const setDifficulty = (level) => {
  aiDifficulty.value = level
  playSound('click')
}

// 设置先手方
const setFirstPlayer = (playerFirst_) => {
  playerFirst.value = playerFirst_
  playSound('click')
}

// 切换音效
const toggleSound = (e) => {
  soundEnabled.value = e.detail.value
}

// 切换坐标显示
const toggleCoordinates = (e) => {
  showCoordinates.value = e.detail.value
  drawBoard()
}

// 播放音效
const playSound = (soundName) => {
  if (soundEnabled.value) {
    audioManager.playSound(soundName)
  }
}

// 格式化时间
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 加载设置
const loadSettings = () => {
  try {
    const settings = uni.getStorageSync('gobang_settings')
    if (settings) {
      aiDifficulty.value = settings.aiDifficulty || 'medium'
      soundEnabled.value = settings.soundEnabled !== false
      showCoordinates.value = settings.showCoordinates || false
      playerFirst.value = settings.playerFirst !== false
    }
  } catch (error) {
    console.warn('加载设置失败:', error)
  }
}

// 保存设置
const saveSettings = () => {
  try {
    const settings = {
      aiDifficulty: aiDifficulty.value,
      soundEnabled: soundEnabled.value,
      showCoordinates: showCoordinates.value,
      playerFirst: playerFirst.value
    }
    uni.setStorageSync('gobang_settings', settings)
  } catch (error) {
    console.warn('保存设置失败:', error)
  }
}

// 保存游戏统计
const saveGameStats = (isWin = false) => {
  try {
    const stats = uni.getStorageSync('game_stats') || {
      totalGames: 0,
      winGames: 0,
      totalTime: 0
    }
    
    stats.totalGames += 1
    if (isWin) stats.winGames += 1
    stats.totalTime += gameTime.value
    
    uni.setStorageSync('game_stats', stats)
    
    // 保存最近游戏记录
    const recentGames = uni.getStorageSync('recent_games') || []
    recentGames.unshift({
      id: Date.now(),
      gameName: '五子棋',
      result: isWin ? 'win' : 'lose',
      timestamp: new Date().toISOString()
    })
    
    // 只保留最近10局
    if (recentGames.length > 10) {
      recentGames.splice(10)
    }
    
    uni.setStorageSync('recent_games', recentGames)
  } catch (error) {
    console.warn('保存游戏统计失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.gobang-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4a5568 0%, #718096 100%);
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
}

.game-header {
  margin-bottom: $spacing-md;
}

.player-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  backdrop-filter: blur(10px);
}

.player {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  opacity: 0.6;
  transition: opacity $transition-normal;
  
  &.active {
    opacity: 1;
  }
}

.player-avatar {
  font-size: 48rpx;
  margin-bottom: 5rpx;
}

.player-name {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  margin-bottom: 5rpx;
}

.player-status {
  font-size: $font-size-sm;
  opacity: 0.8;
}

.game-status {
  text-align: center;
  color: white;
}

.status-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  display: block;
  margin-bottom: 5rpx;
}

.move-count {
  font-size: $font-size-sm;
  opacity: 0.8;
}

.board-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: $spacing-md;
}

.game-board {
  background: #DEB887;
  border-radius: $border-radius-md;
  box-shadow: $shadow-xl;
}

.last-move-marker {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background: #FF6B6B;
  border-radius: 50%;
  border: 2rpx solid white;
  pointer-events: none;
}

.winning-line {
  position: absolute;
  height: 4rpx;
  background: #FFD700;
  border-radius: 2rpx;
  pointer-events: none;
  box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.8);
}

.game-controls {
  display: flex;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;
}

.control-btn {
  flex: 1;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: $border-radius-md;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
  
  &:disabled {
    opacity: 0.5;
  }
  
  &:active:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
  }
}

.btn-icon {
  font-size: $font-size-lg;
  margin-bottom: 5rpx;
}

.btn-text {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

.game-over-modal, .settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
}

.modal-content {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  max-width: 500rpx;
  width: 100%;
  text-align: center;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: $spacing-md;
}

.result-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
  display: block;
}

.result-desc {
  font-size: $font-size-md;
  color: $text-color-secondary;
  margin-bottom: $spacing-md;
  display: block;
}

.game-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: $spacing-lg;
  padding: $spacing-md;
  background: $bg-color-light;
  border-radius: $border-radius-md;
}

.stats-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.result-actions {
  display: flex;
  gap: $spacing-md;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  border: 2rpx solid $border-color-light;
  background: white;
  color: $text-color-primary;
  
  &.primary {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }
}

.modal-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  margin-bottom: $spacing-lg;
  display: block;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md 0;
  border-bottom: 1rpx solid $border-color-light;
  
  &:last-child {
    border-bottom: none;
  }
}

.setting-label {
  font-size: $font-size-md;
  color: $text-color-primary;
}

.difficulty-options, .first-player-options {
  display: flex;
  gap: $spacing-sm;
}

.difficulty-btn, .first-player-btn {
  padding: $spacing-sm $spacing-md;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  background: white;
  color: $text-color-secondary;
  font-size: $font-size-sm;
  
  &.active {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }
}

.close-settings-btn {
  width: 100%;
  height: 80rpx;
  background: $primary-color;
  color: white;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  margin-top: $spacing-lg;
}
</style>
