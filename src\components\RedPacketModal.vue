<template>
  <view v-if="visible" class="redpacket-modal" @tap="closeModal">
    <view class="modal-overlay"></view>
    <view class="modal-content" @tap.stop>
      <!-- 红包容器 -->
      <view class="redpacket-container" :animation="redpacketAnimation">
        <view class="redpacket-bg" :style="{ backgroundColor: redpacketColor }">
          <view class="redpacket-top">
            <text class="redpacket-title">{{ redpacket?.title || '红包' }}</text>
            <text class="redpacket-desc">{{ redpacket?.description || '' }}</text>
          </view>
          
          <view class="redpacket-middle">
            <view class="amount-container" v-if="!opened">
              <text class="amount-symbol">¥</text>
              <text class="amount-value">{{ formatAmount(redpacket?.amount || 0) }}</text>
            </view>
            <view class="opened-content" v-else>
              <view class="coin-animation" :animation="coinAnimation">
                <text class="coin-symbol">💰</text>
              </view>
              <text class="success-text">红包已到账</text>
              <text class="amount-text">¥{{ formatAmount(redpacket?.amount || 0) }}</text>
            </view>
          </view>
          
          <view class="redpacket-bottom">
            <button 
              v-if="!opened" 
              class="open-btn" 
              @tap="openRedPacket"
              :disabled="opening"
            >
              {{ opening ? '开启中...' : '点击开启' }}
            </button>
            <button 
              v-else 
              class="close-btn" 
              @tap="closeModal"
            >
              知道了
            </button>
          </view>
        </view>
        
        <!-- 装饰元素 -->
        <view class="decoration-top"></view>
        <view class="decoration-bottom"></view>
      </view>
      
      <!-- 飘落的金币动画 -->
      <view v-if="showCoins" class="coins-container">
        <view 
          v-for="(coin, index) in coins" 
          :key="index"
          class="falling-coin"
          :style="coin.style"
          :animation="coin.animation"
        >
          💰
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useRedPacketStore } from '../stores/redpacket.js'
import audioManager from '../utils/audio.js'
import animationUtils from '../utils/animation.js'
import redPacketUtils from '../utils/redpacket.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  redpacket: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'opened'])

// Store
const redPacketStore = useRedPacketStore()

// 状态
const opened = ref(false)
const opening = ref(false)
const showCoins = ref(false)
const coins = ref([])

// 动画数据
const redpacketAnimation = ref({})
const coinAnimation = ref({})

// 计算属性
const redpacketColor = computed(() => {
  return redPacketUtils.getRedPacketColor(props.redpacket?.type || 'default')
})

// 监听弹窗显示
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetModal()
    showRedPacketAnimation()
  }
})

// 重置弹窗状态
const resetModal = () => {
  opened.value = false
  opening.value = false
  showCoins.value = false
  coins.value = []
  redpacketAnimation.value = {}
  coinAnimation.value = {}
}

// 显示红包动画
const showRedPacketAnimation = async () => {
  await nextTick()
  
  // 播放出现音效
  redPacketUtils.playRedPacketSound('appear')
  
  // 红包出现动画
  const animation = animationUtils.createAnimation({ duration: 500 })
  animation.scale(0).opacity(0).step({ duration: 0 })
  animation.scale(1).opacity(1).step({ duration: 300 })
  animation.scale(1.1).step({ duration: 100 })
  animation.scale(1).step({ duration: 100 })
  
  redpacketAnimation.value = animation.export()
}

// 开启红包
const openRedPacket = async () => {
  if (opening.value || opened.value) return
  
  opening.value = true
  
  try {
    // 播放开启音效
    redPacketUtils.playRedPacketSound('open')
    
    // 红包开启动画
    const animation = animationUtils.createAnimation({ duration: 800 })
    animation.rotate(5).step({ duration: 100 })
    animation.rotate(-5).step({ duration: 100 })
    animation.rotate(0).step({ duration: 100 })
    animation.scale(1.2).step({ duration: 200 })
    animation.scale(1).step({ duration: 300 })
    
    redpacketAnimation.value = animation.export()
    
    // 延迟显示开启结果
    setTimeout(() => {
      opened.value = true
      opening.value = false
      
      // 播放到账音效
      redPacketUtils.playRedPacketSound('get')
      
      // 显示金币动画
      showCoinAnimation()
      
      // 领取红包
      if (props.redpacket) {
        redPacketStore.claimRedPacket(props.redpacket.id)
        emit('opened', props.redpacket)
      }
    }, 800)
    
  } catch (error) {
    console.error('开启红包失败:', error)
    opening.value = false
  }
}

// 显示金币动画
const showCoinAnimation = () => {
  showCoins.value = true
  
  // 创建多个金币
  const coinCount = 8
  coins.value = []
  
  for (let i = 0; i < coinCount; i++) {
    const coin = {
      style: {
        left: Math.random() * 80 + 10 + '%',
        animationDelay: Math.random() * 0.5 + 's'
      },
      animation: {}
    }
    
    coins.value.push(coin)
    
    // 金币下落动画
    setTimeout(() => {
      const animation = animationUtils.createAnimation({ duration: 1000 })
      animation.translateY(300).opacity(0).step()
      coin.animation = animation.export()
    }, i * 100)
  }
  
  // 金币动画
  const coinAnim = animationUtils.createAnimation({ duration: 600 })
  coinAnim.scale(0).step({ duration: 0 })
  coinAnim.scale(1.5).step({ duration: 200 })
  coinAnim.scale(1).step({ duration: 400 })
  
  coinAnimation.value = coinAnim.export()
  
  // 清理金币
  setTimeout(() => {
    showCoins.value = false
    coins.value = []
  }, 2000)
}

// 关闭弹窗
const closeModal = () => {
  // 关闭动画
  const animation = animationUtils.createAnimation({ duration: 300 })
  animation.scale(0.8).opacity(0).step()
  redpacketAnimation.value = animation.export()
  
  setTimeout(() => {
    emit('close')
    redPacketStore.closeRedPacketModal()
  }, 300)
}

// 格式化金额
const formatAmount = (amount) => {
  return redPacketUtils.formatAmount(amount)
}
</script>

<style lang="scss" scoped>
.redpacket-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: relative;
  width: 600rpx;
  height: 800rpx;
}

.redpacket-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.redpacket-bg {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.redpacket-top {
  flex: 1;
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.redpacket-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.redpacket-desc {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.redpacket-middle {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
}

.amount-container {
  text-align: center;
}

.amount-symbol {
  font-size: 48rpx;
  font-weight: bold;
}

.amount-value {
  font-size: 120rpx;
  font-weight: bold;
  margin-left: 20rpx;
}

.opened-content {
  text-align: center;
}

.coin-animation {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.success-text {
  font-size: 36rpx;
  display: block;
  margin-bottom: 20rpx;
}

.amount-text {
  font-size: 72rpx;
  font-weight: bold;
  display: block;
}

.redpacket-bottom {
  flex: 1;
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.open-btn, .close-btn {
  width: 400rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 40rpx;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.open-btn:active, .close-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
}

.decoration-top, .decoration-bottom {
  position: absolute;
  left: 50%;
  width: 120rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
  transform: translateX(-50%);
}

.decoration-top {
  top: -30rpx;
}

.decoration-bottom {
  bottom: -30rpx;
}

.coins-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.falling-coin {
  position: absolute;
  top: 200rpx;
  font-size: 60rpx;
  animation: coinFall 1s ease-in forwards;
}

@keyframes coinFall {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(300rpx) rotate(360deg);
    opacity: 0;
  }
}
</style>
