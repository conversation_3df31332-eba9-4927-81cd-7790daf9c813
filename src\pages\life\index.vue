<template>
  <view class="page-container">
    <!-- 头部区域 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content safe-area-inset-top">
        <text class="page-title">生活助手</text>
        <text class="page-desc">让生活更便捷的实用工具</text>
      </view>
    </view>
    
    <!-- 工具分类 -->
    <view class="categories-section">
      <view 
        v-for="category in categories" 
        :key="category.id"
        class="category-section"
      >
        <view class="category-header">
          <view class="category-icon">{{ category.icon }}</view>
          <text class="category-title">{{ category.name }}</text>
          <text class="category-desc">{{ category.desc }}</text>
        </view>
        
        <view class="tools-grid">
          <view 
            v-for="tool in category.tools" 
            :key="tool.id"
            class="tool-card"
            @tap="goToTool(tool)"
          >
            <view class="tool-bg" :style="{ background: tool.gradient }"></view>
            <view class="tool-content">
              <view class="tool-icon">{{ tool.icon }}</view>
              <text class="tool-name">{{ tool.name }}</text>
              <text class="tool-desc">{{ tool.desc }}</text>
            </view>
            <view class="tool-status" v-if="tool.status">
              <text class="status-text">{{ tool.status }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 快捷工具 -->
    <view class="quick-tools-section">
      <view class="section-title">
        <text class="title-text">快捷工具</text>
        <text class="title-desc">常用功能快速访问</text>
      </view>
      
      <view class="quick-tools-list">
        <view 
          v-for="tool in quickTools" 
          :key="tool.id"
          class="quick-tool-item card"
          @tap="goToTool(tool)"
        >
          <view class="quick-tool-icon">{{ tool.icon }}</view>
          <view class="quick-tool-info">
            <text class="quick-tool-name">{{ tool.name }}</text>
            <text class="quick-tool-desc">{{ tool.desc }}</text>
          </view>
          <view class="quick-tool-arrow">></view>
        </view>
      </view>
    </view>
    
    <!-- 使用统计 -->
    <view class="stats-section">
      <view class="section-title">
        <text class="title-text">使用统计</text>
        <text class="title-desc">您的工具使用情况</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-card card">
          <view class="stat-icon">🧮</view>
          <view class="stat-info">
            <text class="stat-value">{{ lifeStats.calculatorUsage }}</text>
            <text class="stat-label">计算器使用</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">🔄</view>
          <view class="stat-info">
            <text class="stat-value">{{ lifeStats.converterUsage }}</text>
            <text class="stat-label">转换器使用</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">💪</view>
          <view class="stat-info">
            <text class="stat-value">{{ lifeStats.healthUsage }}</text>
            <text class="stat-label">健康工具</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">🚗</view>
          <view class="stat-info">
            <text class="stat-value">{{ lifeStats.travelUsage }}</text>
            <text class="stat-label">出行工具</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 今日推荐 -->
    <view class="recommend-section">
      <view class="section-title">
        <text class="title-text">今日推荐</text>
        <text class="title-desc">为您推荐实用工具</text>
      </view>
      
      <view class="recommend-card card">
        <view class="recommend-content">
          <view class="recommend-icon">🧮</view>
          <view class="recommend-info">
            <text class="recommend-title">科学计算器</text>
            <text class="recommend-desc">支持复杂数学运算，工程计算必备工具</text>
            <view class="recommend-features">
              <text class="feature-tag">三角函数</text>
              <text class="feature-tag">对数运算</text>
              <text class="feature-tag">历史记录</text>
            </view>
          </view>
        </view>
        <button class="recommend-btn btn-primary" @tap="goToCalculator">
          <text class="btn-text">立即使用</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRedPacketStore } from '../../stores/redpacket.js'
import audioManager from '../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 工具分类
const categories = ref([
  {
    id: 'calculation',
    name: '计算工具',
    desc: '各种计算和数学工具',
    icon: '🧮',
    tools: [
      {
        id: 'calculator',
        name: '科学计算器',
        desc: '支持复杂运算',
        icon: '🧮',
        path: '/pages/life/calculator/index',
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        status: '完整版'
      },
      {
        id: 'converter',
        name: '单位转换',
        desc: '长度重量温度转换',
        icon: '🔄',
        path: '/pages/life/converter/index',
        gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        status: '完整版'
      }
    ]
  },
  {
    id: 'health',
    name: '健康工具',
    desc: '健康管理和计算工具',
    icon: '💪',
    tools: [
      {
        id: 'bmi',
        name: 'BMI计算器',
        desc: '身体质量指数计算',
        icon: '⚖️',
        path: '/pages/life/bmi/index',
        gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        status: '规划中'
      },
      {
        id: 'calorie',
        name: '卡路里计算',
        desc: '每日热量需求计算',
        icon: '🔥',
        path: '/pages/life/calorie/index',
        gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        status: '规划中'
      }
    ]
  },
  {
    id: 'travel',
    name: '出行工具',
    desc: '出行相关实用工具',
    icon: '🚗',
    tools: [
      {
        id: 'weather',
        name: '天气查询',
        desc: '实时天气预报',
        icon: '🌤️',
        path: '/pages/life/weather/index',
        gradient: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
        status: '规划中'
      },
      {
        id: 'map',
        name: '地图导航',
        desc: '路线规划导航',
        icon: '🗺️',
        path: '/pages/life/map/index',
        gradient: 'linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%)',
        status: '规划中'
      }
    ]
  }
])

// 快捷工具
const quickTools = ref([
  {
    id: 'calculator',
    name: '科学计算器',
    desc: '快速进行数学计算',
    icon: '🧮',
    path: '/pages/life/calculator/index'
  },
  {
    id: 'converter',
    name: '单位转换',
    desc: '长度、重量、温度转换',
    icon: '🔄',
    path: '/pages/life/converter/index'
  }
])

// 使用统计
const lifeStats = ref({
  calculatorUsage: 0,
  converterUsage: 0,
  healthUsage: 0,
  travelUsage: 0
})

// 页面加载
onMounted(() => {
  loadLifeStats()
  audioManager.playSound('page_turn')
})

// 加载使用统计
const loadLifeStats = () => {
  try {
    const stats = uni.getStorageSync('life_stats')
    if (stats) {
      lifeStats.value = { ...lifeStats.value, ...stats }
    }
  } catch (error) {
    console.warn('加载生活工具统计失败:', error)
  }
}

// 跳转到工具
const goToTool = (tool) => {
  audioManager.playSound('button_click')
  
  if (tool.status === '规划中') {
    uni.showToast({
      title: '功能规划中，敬请期待',
      icon: 'none'
    })
    return
  }
  
  // 触发使用工具统计
  redPacketStore.onToolUsed()
  
  // 记录使用
  recordToolUsage(tool)
  
  uni.navigateTo({
    url: tool.path
  })
}

// 跳转到计算器
const goToCalculator = () => {
  const calculatorTool = {
    id: 'calculator',
    path: '/pages/life/calculator/index'
  }
  goToTool(calculatorTool)
}

// 记录工具使用
const recordToolUsage = (tool) => {
  try {
    const stats = uni.getStorageSync('life_stats') || {
      calculatorUsage: 0,
      converterUsage: 0,
      healthUsage: 0,
      travelUsage: 0
    }
    
    // 根据工具类型更新统计
    switch (tool.id) {
      case 'calculator':
        stats.calculatorUsage += 1
        break
      case 'converter':
        stats.converterUsage += 1
        break
      case 'bmi':
      case 'calorie':
        stats.healthUsage += 1
        break
      case 'weather':
      case 'map':
        stats.travelUsage += 1
        break
    }
    
    uni.setStorageSync('life_stats', stats)
    loadLifeStats()
  } catch (error) {
    console.warn('记录工具使用失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

.header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.header-content {
  position: relative;
  height: 100%;
  padding: 40rpx $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  font-size: $font-size-md;
  opacity: 0.9;
}

.categories-section, .quick-tools-section, .stats-section, .recommend-section {
  padding: $spacing-md;
}

.category-section {
  margin-bottom: $spacing-xl;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-md;
  padding: $spacing-md;
  background: rgba(255, 255, 255, 0.8);
  border-radius: $border-radius-lg;
  backdrop-filter: blur(10px);
}

.category-icon {
  font-size: 48rpx;
  margin-right: $spacing-md;
}

.category-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-right: $spacing-sm;
}

.category-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  flex: 1;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
}

.tool-card {
  position: relative;
  height: 200rpx;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-md;
}

.tool-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.tool-content {
  position: relative;
  height: 100%;
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.tool-icon {
  font-size: 60rpx;
  margin-bottom: $spacing-sm;
}

.tool-name {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  margin-bottom: 5rpx;
  display: block;
}

.tool-desc {
  font-size: $font-size-sm;
  opacity: 0.9;
  display: block;
}

.tool-status {
  position: absolute;
  top: $spacing-sm;
  right: $spacing-sm;
  padding: 4rpx 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  backdrop-filter: blur(5px);
}

.status-text {
  font-size: 20rpx;
  color: white;
  font-weight: $font-weight-bold;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.title-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.quick-tools-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.quick-tool-item {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  gap: $spacing-md;
}

.quick-tool-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.quick-tool-info {
  flex: 1;
}

.quick-tool-name {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.quick-tool-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.quick-tool-arrow {
  font-size: $font-size-lg;
  color: $text-color-light;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  gap: $spacing-md;
}

.stat-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.recommend-card {
  padding: $spacing-lg;
}

.recommend-content {
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.recommend-icon {
  font-size: 80rpx;
  width: 120rpx;
  text-align: center;
  flex-shrink: 0;
}

.recommend-info {
  flex: 1;
}

.recommend-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.recommend-desc {
  font-size: $font-size-md;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-md;
  line-height: 1.5;
}

.recommend-features {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.feature-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  background: $bg-color-light;
  color: $text-color-secondary;
  border-radius: $border-radius-sm;
}

.recommend-btn {
  width: 100%;
  height: 80rpx;
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
}
</style>
