<template>
  <view class="xiaoxiaole-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="game-title">消消乐</text>
        <text class="game-subtitle">休闲益智消除游戏</text>
      </view>
    </view>
    
    <!-- 开发中提示 -->
    <view class="development-notice">
      <view class="notice-card">
        <view class="notice-icon">🎮</view>
        <text class="notice-title">功能开发中</text>
        <text class="notice-desc">消消乐游戏正在精心制作中，即将与您见面！</text>
        
        <view class="features-preview">
          <text class="features-title">即将推出的功能：</text>
          <view class="feature-list">
            <view class="feature-item">
              <text class="feature-icon">🍭</text>
              <text class="feature-text">多彩糖果消除</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">💥</text>
              <text class="feature-text">特效道具系统</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🎯</text>
              <text class="feature-text">关卡挑战模式</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">⭐</text>
              <text class="feature-text">三星评价系统</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🏆</text>
              <text class="feature-text">排行榜竞技</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🎁</text>
              <text class="feature-text">每日奖励</text>
            </view>
          </view>
        </view>
        
        <view class="progress-section">
          <text class="progress-title">开发进度</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: '25%' }"></view>
          </view>
          <text class="progress-text">25% 完成</text>
        </view>
      </view>
    </view>
    
    <!-- 游戏玩法介绍 -->
    <view class="gameplay-section">
      <view class="section-title">
        <text class="title-text">游戏玩法</text>
      </view>
      
      <view class="gameplay-content">
        <view class="gameplay-item">
          <view class="gameplay-icon">🔄</view>
          <view class="gameplay-info">
            <text class="gameplay-title">交换消除</text>
            <text class="gameplay-desc">交换相邻糖果，形成3个或更多相同糖果的连线来消除</text>
          </view>
        </view>
        
        <view class="gameplay-item">
          <view class="gameplay-icon">⚡</view>
          <view class="gameplay-info">
            <text class="gameplay-title">特殊糖果</text>
            <text class="gameplay-desc">消除4个以上糖果可产生特殊糖果，拥有强大的消除能力</text>
          </view>
        </view>
        
        <view class="gameplay-item">
          <view class="gameplay-icon">🎯</view>
          <view class="gameplay-info">
            <text class="gameplay-title">目标挑战</text>
            <text class="gameplay-desc">每个关卡都有不同的目标，在限定步数内完成挑战</text>
          </view>
        </view>
        
        <view class="gameplay-item">
          <view class="gameplay-icon">💎</view>
          <view class="gameplay-info">
            <text class="gameplay-title">道具助力</text>
            <text class="gameplay-desc">使用各种道具帮助您通过困难关卡</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 预览游戏界面 -->
    <view class="preview-section">
      <view class="section-title">
        <text class="title-text">游戏预览</text>
      </view>
      
      <view class="preview-board">
        <view class="board-grid">
          <view 
            v-for="(candy, index) in previewCandies" 
            :key="index"
            class="candy-cell"
            :style="{ backgroundColor: candy.color }"
          >
            <text class="candy-icon">{{ candy.icon }}</text>
          </view>
        </view>
        
        <view class="preview-ui">
          <view class="score-display">
            <text class="score-label">分数</text>
            <text class="score-value">12,580</text>
          </view>
          
          <view class="moves-display">
            <text class="moves-label">剩余步数</text>
            <text class="moves-value">15</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="actions">
      <button class="action-btn primary" @tap="goBack">
        <text class="btn-text">返回游戏列表</text>
      </button>
      
      <button class="action-btn" @tap="showNotification">
        <text class="btn-text">开发完成通知我</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import audioManager from '../../../utils/audio.js'

// 预览糖果数据
const previewCandies = ref([])

onMounted(() => {
  generatePreviewCandies()
})

// 生成预览糖果
const generatePreviewCandies = () => {
  const candyTypes = [
    { icon: '🍭', color: '#FF6B9D' },
    { icon: '🍬', color: '#4ECDC4' },
    { icon: '🧁', color: '#45B7D1' },
    { icon: '🍪', color: '#F9CA24' },
    { icon: '🍰', color: '#6C5CE7' },
    { icon: '🎂', color: '#A0E7E5' }
  ]
  
  const candies = []
  for (let i = 0; i < 64; i++) { // 8x8网格
    const randomCandy = candyTypes[Math.floor(Math.random() * candyTypes.length)]
    candies.push({ ...randomCandy })
  }
  
  previewCandies.value = candies
}

// 返回游戏列表
const goBack = () => {
  audioManager.playSound('button_click')
  uni.navigateBack()
}

// 显示通知设置
const showNotification = () => {
  audioManager.playSound('click')
  uni.showModal({
    title: '开发通知',
    content: '我们会在消消乐功能开发完成后第一时间通知您！期待与您一起享受消除的乐趣！',
    showCancel: false,
    confirmText: '好的'
  })
}
</script>

<style lang="scss" scoped>
.xiaoxiaole-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #9f7aea 0%, #d53f8c 100%);
}

.header {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #9f7aea 0%, #d53f8c 100%);
}

.header-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.game-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.game-subtitle {
  font-size: $font-size-md;
  opacity: 0.8;
}

.development-notice {
  padding: $spacing-md;
  margin-top: -40rpx;
  position: relative;
  z-index: 1;
}

.notice-card {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-lg;
  text-align: center;
}

.notice-icon {
  font-size: 120rpx;
  margin-bottom: $spacing-md;
}

.notice-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.notice-desc {
  font-size: $font-size-md;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-lg;
}

.features-preview {
  text-align: left;
  margin-bottom: $spacing-lg;
}

.features-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-md;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-sm;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: $bg-color-light;
  border-radius: $border-radius-md;
}

.feature-icon {
  font-size: $font-size-lg;
}

.feature-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.progress-section {
  text-align: center;
}

.progress-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: $bg-color-light;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: $spacing-sm;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #9f7aea 0%, #d53f8c 100%);
  transition: width $transition-normal;
}

.progress-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.gameplay-section, .preview-section {
  padding: $spacing-md;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: white;
}

.gameplay-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  backdrop-filter: blur(10px);
}

.gameplay-item {
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
  margin-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.gameplay-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
  flex-shrink: 0;
}

.gameplay-info {
  flex: 1;
}

.gameplay-title {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: white;
  display: block;
  margin-bottom: 5rpx;
}

.gameplay-desc {
  font-size: $font-size-sm;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  line-height: 1.5;
}

.preview-board {
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  backdrop-filter: blur(10px);
}

.board-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4rpx;
  margin-bottom: $spacing-md;
  background: rgba(0, 0, 0, 0.2);
  padding: $spacing-sm;
  border-radius: $border-radius-md;
}

.candy-cell {
  aspect-ratio: 1;
  border-radius: $border-radius-sm;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.candy-icon {
  font-size: 24rpx;
}

.preview-ui {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.score-display, .moves-display {
  text-align: center;
  color: white;
}

.score-label, .moves-label {
  font-size: $font-size-sm;
  opacity: 0.8;
  display: block;
  margin-bottom: 5rpx;
}

.score-value, .moves-value {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  display: block;
}

.actions {
  padding: $spacing-md;
  display: flex;
  gap: $spacing-md;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  
  &.primary {
    background: linear-gradient(135deg, #9f7aea 0%, #d53f8c 100%);
    color: white;
  }
  
  &:not(.primary) {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2rpx solid rgba(255, 255, 255, 0.3);
  }
}
</style>
