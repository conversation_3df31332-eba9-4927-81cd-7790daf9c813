<template>
  <view class="page-container">
    <!-- 头部区域 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content safe-area-inset-top">
        <view class="welcome-section">
          <text class="welcome-text">欢迎使用</text>
          <text class="app-title">多功能工具箱</text>
          <text class="app-desc">生活、娱乐、工作一站式解决方案</text>
        </view>

        <!-- 红包统计卡片 -->
        <view class="redpacket-card" @tap="goToRedPacket">
          <view class="redpacket-info">
            <view class="redpacket-amount">
              <text class="amount-label">今日收益</text>
              <text class="amount-value">¥{{ formatAmount(redPacketStore.todayAmount) }}</text>
            </view>
            <view class="redpacket-count">
              <text class="count-text">总红包: {{ redPacketStore.totalCount }}</text>
            </view>
          </view>
          <view class="redpacket-icon">🧧</view>
        </view>
      </view>
    </view>

    <!-- 签到区域 -->
    <view class="signin-section">
      <view class="signin-card card" @tap="handleSignIn">
        <view class="signin-content">
          <view class="signin-info">
            <text class="signin-title">每日签到</text>
            <text class="signin-desc">
              {{ redPacketStore.canSignInToday ? `签到获得¥${formatAmount(redPacketStore.nextSignInAmount)}` : '今日已签到' }}
            </text>
          </view>
          <view class="signin-btn" :class="{ disabled: !redPacketStore.canSignInToday }">
            <text class="signin-btn-text">{{ redPacketStore.canSignInToday ? '签到' : '已签到' }}</text>
          </view>
        </view>
        <view class="signin-days">
          <text class="days-text">连续签到 {{ redPacketStore.signInDays }} 天</text>
        </view>
      </view>
    </view>

    <!-- 功能模块 -->
    <view class="modules-section">
      <view class="section-title">
        <text class="title-text">功能模块</text>
        <text class="title-desc">选择您需要的工具</text>
      </view>

      <view class="modules-grid">
        <view
          v-for="module in modules"
          :key="module.id"
          class="module-card"
          @tap="goToModule(module)"
        >
          <view class="module-bg" :style="{ background: module.gradient }"></view>
          <view class="module-content">
            <text class="module-icon">{{ module.icon }}</text>
            <text class="module-name">{{ module.name }}</text>
            <text class="module-desc">{{ module.desc }}</text>
          </view>
          <view class="module-badge" v-if="module.badge">
            <text class="badge-text">{{ module.badge }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 任务进度 -->
    <view class="tasks-section">
      <view class="section-title">
        <text class="title-text">每日任务</text>
        <text class="title-desc">完成任务获得红包奖励</text>
      </view>

      <view class="tasks-list">
        <view
          v-for="(task, key) in taskCompletion"
          :key="key"
          class="task-item card"
        >
          <view class="task-info">
            <text class="task-name">{{ getTaskName(key) }}</text>
            <text class="task-progress">{{ task.progress }}/{{ task.total }}</text>
          </view>
          <view class="task-reward">
            <text class="reward-text">¥{{ formatAmount(task.reward[0]) }}-{{ formatAmount(task.reward[1]) }}</text>
          </view>
          <view class="task-status" :class="{ completed: task.completed }">
            <text class="status-text">{{ task.completed ? '已完成' : '进行中' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 红包弹窗 -->
    <RedPacketModal
      :visible="redPacketStore.showRedPacketModal"
      :redpacket="redPacketStore.currentRedPacket"
      @close="redPacketStore.closeRedPacketModal"
      @opened="onRedPacketOpened"
    />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRedPacketStore } from '../../stores/redpacket.js'
import RedPacketModal from '../../components/RedPacketModal.vue'
import redPacketUtils from '../../utils/redpacket.js'
import audioManager from '../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 功能模块配置
const modules = ref([
  {
    id: 'entertainment',
    name: '娱乐游戏',
    desc: '象棋、五子棋等',
    icon: '🎮',
    path: '/pages/entertainment/index',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    badge: 'HOT'
  },
  {
    id: 'work',
    name: '工作工具',
    desc: '证件号、去水印',
    icon: '💼',
    path: '/pages/work/index',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    id: 'life',
    name: '生活助手',
    desc: '计算器、转换器',
    icon: '🏠',
    path: '/pages/life/index',
    gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
  },
  {
    id: 'redpacket',
    name: '我的红包',
    desc: '红包记录、统计',
    icon: '🧧',
    path: '/pages/redpacket/index',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  }
])

// 计算属性
const taskCompletion = computed(() => redPacketStore.taskCompletion)

// 页面加载
onMounted(() => {
  // 检查新用户红包
  checkNewUserRedPacket()
})

// 检查新用户红包
const checkNewUserRedPacket = () => {
  const hasShownNewUser = uni.getStorageSync('has_shown_new_user_redpacket')
  if (!hasShownNewUser && redPacketStore.redPacketHistory.length === 0) {
    setTimeout(() => {
      redPacketStore.giveNewUserRedPacket()
      uni.setStorageSync('has_shown_new_user_redpacket', true)
    }, 1000)
  }
}

// 签到处理
const handleSignIn = () => {
  if (!redPacketStore.canSignInToday) {
    uni.showToast({
      title: '今日已签到',
      icon: 'none'
    })
    return
  }

  const success = redPacketStore.signIn()
  if (success) {
    audioManager.playSound('success')
    uni.showToast({
      title: '签到成功！',
      icon: 'success'
    })
  }
}

// 跳转到模块
const goToModule = (module) => {
  audioManager.playSound('button_click')

  // 触发使用工具统计
  if (module.id !== 'redpacket') {
    redPacketStore.onToolUsed()
  }

  uni.navigateTo({
    url: module.path
  })
}

// 跳转到红包页面
const goToRedPacket = () => {
  audioManager.playSound('button_click')
  uni.navigateTo({
    url: '/pages/redpacket/index'
  })
}

// 红包开启回调
const onRedPacketOpened = (redpacket) => {
  console.log('红包已开启:', redpacket)
}

// 格式化金额
const formatAmount = (amount) => {
  return redPacketUtils.formatAmount(amount)
}

// 获取任务名称
const getTaskName = (taskType) => {
  const names = {
    useTools: '使用工具',
    playGames: '游戏娱乐',
    shareApp: '分享应用'
  }
  return names[taskType] || taskType
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

.header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $gradient-primary;
}

.header-content {
  position: relative;
  height: 100%;
  padding: 40rpx $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.welcome-section {
  color: white;
  text-align: center;
  margin-top: 40rpx;
}

.welcome-text {
  font-size: $font-size-md;
  opacity: 0.9;
  display: block;
  margin-bottom: 10rpx;
}

.app-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  display: block;
  margin-bottom: 10rpx;
}

.app-desc {
  font-size: $font-size-sm;
  opacity: 0.8;
  display: block;
}

.redpacket-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(10px);
}

.redpacket-info {
  color: white;
}

.amount-label {
  font-size: $font-size-sm;
  opacity: 0.9;
  display: block;
}

.amount-value {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  display: block;
  margin: 5rpx 0;
}

.count-text {
  font-size: $font-size-xs;
  opacity: 0.8;
}

.redpacket-icon {
  font-size: 60rpx;
}

.signin-section {
  padding: $spacing-md;
  margin-top: -60rpx;
  position: relative;
  z-index: 1;
}

.signin-card {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  box-shadow: $shadow-lg;
}

.signin-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-sm;
}

.signin-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.signin-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.signin-btn {
  background: $gradient-primary;
  color: white;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;

  &.disabled {
    background: $bg-color-light;
    color: $text-color-light;
  }
}

.days-text {
  font-size: $font-size-xs;
  color: $text-color-light;
}

.modules-section, .tasks-section {
  padding: $spacing-md;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.title-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
}

.module-card {
  position: relative;
  height: 200rpx;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-md;
}

.module-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.module-content {
  position: relative;
  height: 100%;
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.module-icon {
  font-size: 60rpx;
  margin-bottom: $spacing-sm;
}

.module-name {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  margin-bottom: 5rpx;
  display: block;
}

.module-desc {
  font-size: $font-size-xs;
  opacity: 0.9;
  display: block;
}

.module-badge {
  position: absolute;
  top: $spacing-sm;
  right: $spacing-sm;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(5px);
}

.badge-text {
  font-size: 20rpx;
  color: white;
  font-weight: $font-weight-bold;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.task-progress {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.task-reward {
  margin: 0 $spacing-md;
}

.reward-text {
  font-size: $font-size-sm;
  color: $warning-color;
  font-weight: $font-weight-medium;
}

.task-status {
  padding: 4rpx 12rpx;
  border-radius: $border-radius-sm;
  background: $bg-color-light;

  &.completed {
    background: $success-color;
  }
}

.status-text {
  font-size: $font-size-xs;
  color: $text-color-secondary;

  .completed & {
    color: white;
  }
}
</style>
