<template>
  <view class="idcard-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="page-title">证件号生成器</text>
        <text class="page-desc">生成符合国标的身份证号码</text>
      </view>
    </view>
    
    <!-- 生成配置 -->
    <view class="config-section">
      <view class="config-card card">
        <view class="config-header">
          <text class="config-title">生成配置</text>
          <button class="reset-btn" @tap="resetConfig">重置</button>
        </view>
        
        <view class="config-form">
          <!-- 生成数量 -->
          <view class="form-item">
            <text class="form-label">生成数量</text>
            <view class="quantity-controls">
              <button class="quantity-btn" @tap="decreaseQuantity">-</button>
              <input 
                class="quantity-input" 
                type="number" 
                v-model="config.quantity"
                @input="onQuantityChange"
              />
              <button class="quantity-btn" @tap="increaseQuantity">+</button>
            </view>
          </view>
          
          <!-- 省份选择 -->
          <view class="form-item">
            <text class="form-label">省份地区</text>
            <picker 
              :value="provinceIndex" 
              :range="provinces" 
              @change="onProvinceChange"
            >
              <view class="picker-display">
                {{ selectedProvince || '随机省份' }}
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <!-- 性别选择 -->
          <view class="form-item">
            <text class="form-label">性别</text>
            <view class="gender-options">
              <button 
                v-for="option in genderOptions" 
                :key="option.value"
                class="gender-btn"
                :class="{ active: config.gender === option.value }"
                @tap="setGender(option.value)"
              >
                {{ option.label }}
              </button>
            </view>
          </view>
          
          <!-- 年龄范围 -->
          <view class="form-item">
            <text class="form-label">年龄范围</text>
            <view class="age-range">
              <input 
                class="age-input" 
                type="number" 
                placeholder="最小年龄"
                v-model="config.minAge"
              />
              <text class="age-separator">-</text>
              <input 
                class="age-input" 
                type="number" 
                placeholder="最大年龄"
                v-model="config.maxAge"
              />
            </view>
          </view>
        </view>
        
        <!-- 生成按钮 -->
        <button class="generate-btn btn-primary" @tap="generateIdCards" :disabled="generating">
          <text class="btn-text">{{ generating ? '生成中...' : '开始生成' }}</text>
        </button>
      </view>
    </view>
    
    <!-- 验证工具 -->
    <view class="validate-section">
      <view class="validate-card card">
        <text class="validate-title">身份证验证</text>
        <view class="validate-form">
          <input 
            class="validate-input" 
            placeholder="请输入18位身份证号"
            v-model="validateInput"
            maxlength="18"
          />
          <button class="validate-btn btn-outline" @tap="validateIdCard">验证</button>
        </view>
        
        <view v-if="validateResult" class="validate-result">
          <view class="result-status" :class="{ valid: validateResult.valid }">
            <text class="status-icon">{{ validateResult.valid ? '✓' : '✗' }}</text>
            <text class="status-text">{{ validateResult.valid ? '验证通过' : '验证失败' }}</text>
          </view>
          
          <view v-if="validateResult.error" class="error-message">
            <text class="error-text">{{ validateResult.error }}</text>
          </view>
          
          <view v-if="validateResult.info" class="info-details">
            <view class="info-item">
              <text class="info-label">地区：</text>
              <text class="info-value">{{ validateResult.info.areaName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">出生日期：</text>
              <text class="info-value">{{ validateResult.info.birthDate }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">年龄：</text>
              <text class="info-value">{{ validateResult.info.age }}岁</text>
            </view>
            <view class="info-item">
              <text class="info-label">性别：</text>
              <text class="info-value">{{ validateResult.info.gender }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 生成结果 -->
    <view v-if="generatedCards.length > 0" class="results-section">
      <view class="results-header">
        <text class="results-title">生成结果 ({{ generatedCards.length }}条)</text>
        <view class="results-actions">
          <button class="action-btn" @tap="copyAll">复制全部</button>
          <button class="action-btn" @tap="exportData">导出</button>
          <button class="action-btn" @tap="clearResults">清空</button>
        </view>
      </view>
      
      <view class="results-list">
        <view 
          v-for="(item, index) in generatedCards" 
          :key="index"
          class="result-item card"
        >
          <view class="result-header">
            <text class="result-number">{{ index + 1 }}</text>
            <text class="result-idcard">{{ formatIdCard(item.idCard) }}</text>
            <button class="copy-btn" @tap="copyIdCard(item.idCard)">复制</button>
          </view>
          
          <view class="result-details">
            <view class="detail-row">
              <text class="detail-label">地区：</text>
              <text class="detail-value">{{ item.info.areaName }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">出生：</text>
              <text class="detail-value">{{ item.info.birthDate }} ({{ item.info.age }}岁)</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">性别：</text>
              <text class="detail-value">{{ item.info.gender }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 统计信息 -->
    <view v-if="statistics" class="stats-section">
      <view class="stats-card card">
        <text class="stats-title">统计信息</text>
        
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-label">总数</text>
            <text class="stat-value">{{ statistics.total }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">男性</text>
            <text class="stat-value">{{ statistics.genderCount.male }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">女性</text>
            <text class="stat-value">{{ statistics.genderCount.female }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">平均年龄</text>
            <text class="stat-value">{{ averageAge }}岁</text>
          </view>
        </view>
        
        <view class="age-distribution">
          <text class="distribution-title">年龄分布</text>
          <view class="distribution-bars">
            <view 
              v-for="(count, group) in statistics.ageGroups" 
              :key="group"
              class="distribution-bar"
            >
              <text class="bar-label">{{ group }}</text>
              <view class="bar-container">
                <view 
                  class="bar-fill" 
                  :style="{ width: (count / statistics.total * 100) + '%' }"
                ></view>
              </view>
              <text class="bar-count">{{ count }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRedPacketStore } from '../../../stores/redpacket.js'
import idCardGenerator from '../../../utils/idcard-generator.js'
import audioManager from '../../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 配置数据
const config = ref({
  quantity: 10,
  province: null,
  gender: null, // null, 'male', 'female'
  minAge: 18,
  maxAge: 65
})

// 省份数据
const provinces = ref(['随机省份'])
const provinceIndex = ref(0)
const selectedProvince = ref('')

// 性别选项
const genderOptions = ref([
  { label: '随机', value: null },
  { label: '男性', value: 'male' },
  { label: '女性', value: 'female' }
])

// 生成状态
const generating = ref(false)
const generatedCards = ref([])
const statistics = ref(null)

// 验证相关
const validateInput = ref('')
const validateResult = ref(null)

// 计算属性
const averageAge = computed(() => {
  if (!generatedCards.value.length) return 0
  const totalAge = generatedCards.value.reduce((sum, item) => sum + item.info.age, 0)
  return Math.round(totalAge / generatedCards.value.length)
})

// 页面加载
onMounted(() => {
  initProvinces()
  audioManager.playSound('page_turn')
})

// 初始化省份列表
const initProvinces = () => {
  const provinceList = idCardGenerator.getProvinces()
  provinces.value = ['随机省份', ...provinceList]
}

// 省份选择
const onProvinceChange = (e) => {
  provinceIndex.value = e.detail.value
  if (e.detail.value === 0) {
    selectedProvince.value = ''
    config.value.province = null
  } else {
    selectedProvince.value = provinces.value[e.detail.value]
    config.value.province = selectedProvince.value
  }
}

// 数量控制
const decreaseQuantity = () => {
  if (config.value.quantity > 1) {
    config.value.quantity--
  }
  audioManager.playSound('click')
}

const increaseQuantity = () => {
  if (config.value.quantity < 1000) {
    config.value.quantity++
  }
  audioManager.playSound('click')
}

const onQuantityChange = (e) => {
  const value = parseInt(e.detail.value)
  if (value >= 1 && value <= 1000) {
    config.value.quantity = value
  }
}

// 设置性别
const setGender = (gender) => {
  config.value.gender = gender
  audioManager.playSound('click')
}

// 重置配置
const resetConfig = () => {
  config.value = {
    quantity: 10,
    province: null,
    gender: null,
    minAge: 18,
    maxAge: 65
  }
  provinceIndex.value = 0
  selectedProvince.value = ''
  audioManager.playSound('click')
}

// 生成身份证号
const generateIdCards = async () => {
  if (generating.value) return
  
  generating.value = true
  audioManager.playSound('button_click')
  
  try {
    // 计算年份范围
    const currentYear = new Date().getFullYear()
    const startYear = currentYear - config.value.maxAge
    const endYear = currentYear - config.value.minAge
    
    const options = {
      province: config.value.province,
      gender: config.value.gender,
      startYear,
      endYear
    }
    
    // 分批生成，避免界面卡顿
    const batchSize = 50
    const results = []
    
    for (let i = 0; i < config.value.quantity; i += batchSize) {
      const currentBatch = Math.min(batchSize, config.value.quantity - i)
      const batchResults = idCardGenerator.generateBatch(currentBatch, options)
      results.push(...batchResults)
      
      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 10))
    }
    
    generatedCards.value = results
    statistics.value = idCardGenerator.getStatistics(results)
    
    // 触发工具使用统计
    redPacketStore.onToolUsed()
    
    audioManager.playSound('success')
    uni.showToast({
      title: `成功生成${results.length}条记录`,
      icon: 'success'
    })
    
  } catch (error) {
    console.error('生成失败:', error)
    audioManager.playSound('error')
    uni.showToast({
      title: '生成失败，请重试',
      icon: 'error'
    })
  } finally {
    generating.value = false
  }
}

// 验证身份证号
const validateIdCard = () => {
  if (!validateInput.value.trim()) {
    uni.showToast({
      title: '请输入身份证号',
      icon: 'none'
    })
    return
  }
  
  validateResult.value = idCardGenerator.validate(validateInput.value.trim())
  audioManager.playSound(validateResult.value.valid ? 'success' : 'error')
}

// 复制身份证号
const copyIdCard = (idCard) => {
  uni.setClipboardData({
    data: idCard,
    success: () => {
      audioManager.playSound('success')
      uni.showToast({
        title: '已复制到剪贴板',
        icon: 'success'
      })
    }
  })
}

// 复制全部
const copyAll = () => {
  const allIdCards = generatedCards.value.map(item => item.idCard).join('\n')
  uni.setClipboardData({
    data: allIdCards,
    success: () => {
      audioManager.playSound('success')
      uni.showToast({
        title: '已复制全部到剪贴板',
        icon: 'success'
      })
    }
  })
}

// 导出数据
const exportData = () => {
  uni.showActionSheet({
    itemList: ['导出为CSV', '导出为JSON'],
    success: (res) => {
      let content = ''
      let filename = ''
      
      if (res.tapIndex === 0) {
        content = idCardGenerator.exportToCSV(generatedCards.value)
        filename = `身份证号_${new Date().toLocaleDateString()}.csv`
      } else {
        content = idCardGenerator.exportToJSON(generatedCards.value)
        filename = `身份证号_${new Date().toLocaleDateString()}.json`
      }
      
      // #ifdef H5
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = filename
      link.click()
      // #endif
      
      // #ifdef MP-WEIXIN
      uni.showModal({
        title: '导出功能',
        content: '小程序暂不支持文件导出，请在H5版本中使用此功能',
        showCancel: false
      })
      // #endif
      
      audioManager.playSound('success')
    }
  })
}

// 清空结果
const clearResults = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有生成结果吗？',
    success: (res) => {
      if (res.confirm) {
        generatedCards.value = []
        statistics.value = null
        audioManager.playSound('click')
      }
    }
  })
}

// 格式化身份证号显示
const formatIdCard = (idCard) => {
  return idCardGenerator.formatIdCard(idCard)
}
</script>

<style lang="scss" scoped>
.idcard-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

.header {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  font-size: $font-size-md;
  opacity: 0.9;
}

.config-section, .validate-section, .results-section, .stats-section {
  padding: $spacing-md;
}

.config-card, .validate-card, .stats-card {
  padding: $spacing-lg;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-lg;
}

.config-title, .validate-title, .stats-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.reset-btn {
  padding: $spacing-sm $spacing-md;
  background: $bg-color-light;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.config-form {
  margin-bottom: $spacing-lg;
}

.form-item {
  margin-bottom: $spacing-lg;
}

.form-label {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background: $primary-color;
  color: white;
  border-radius: $border-radius-sm;
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
}

.quantity-input {
  flex: 1;
  height: 60rpx;
  padding: 0 $spacing-sm;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  text-align: center;
  font-size: $font-size-md;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-sm $spacing-md;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  background: white;
}

.picker-arrow {
  color: $text-color-light;
}

.gender-options {
  display: flex;
  gap: $spacing-sm;
}

.gender-btn {
  flex: 1;
  height: 60rpx;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  background: white;
  color: $text-color-secondary;
  font-size: $font-size-sm;
  
  &.active {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }
}

.age-range {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.age-input {
  flex: 1;
  height: 60rpx;
  padding: 0 $spacing-sm;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  text-align: center;
  font-size: $font-size-md;
}

.age-separator {
  color: $text-color-secondary;
  font-weight: $font-weight-bold;
}

.generate-btn {
  width: 100%;
  height: 80rpx;
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  
  &:disabled {
    opacity: 0.6;
  }
}

.validate-form {
  display: flex;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;
}

.validate-input {
  flex: 1;
  height: 60rpx;
  padding: 0 $spacing-sm;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-sm;
  font-size: $font-size-md;
}

.validate-btn {
  padding: 0 $spacing-lg;
  height: 60rpx;
  border: 2rpx solid $primary-color;
  border-radius: $border-radius-sm;
  background: white;
  color: $primary-color;
  font-size: $font-size-sm;
}

.validate-result {
  padding: $spacing-md;
  background: $bg-color-light;
  border-radius: $border-radius-md;
}

.result-status {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;
  
  &.valid {
    color: $success-color;
  }
  
  &:not(.valid) {
    color: $danger-color;
  }
}

.status-icon {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
}

.status-text {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
}

.error-message {
  margin-bottom: $spacing-sm;
}

.error-text {
  font-size: $font-size-sm;
  color: $danger-color;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  min-width: 120rpx;
}

.info-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
  font-weight: $font-weight-medium;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-md;
}

.results-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.results-actions {
  display: flex;
  gap: $spacing-sm;
}

.action-btn {
  padding: $spacing-sm $spacing-md;
  background: $bg-color-light;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.result-item {
  padding: $spacing-md;
}

.result-header {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;
}

.result-number {
  width: 60rpx;
  height: 60rpx;
  background: $primary-color;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-sm;
  font-weight: $font-weight-bold;
  flex-shrink: 0;
}

.result-idcard {
  flex: 1;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  font-family: 'Courier New', monospace;
}

.copy-btn {
  padding: $spacing-sm $spacing-md;
  background: $success-color;
  color: white;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  min-width: 80rpx;
}

.detail-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.stat-item {
  text-align: center;
  padding: $spacing-md;
  background: $bg-color-light;
  border-radius: $border-radius-md;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
  margin-bottom: 5rpx;
}

.stat-value {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
}

.age-distribution {
  margin-top: $spacing-lg;
}

.distribution-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-md;
}

.distribution-bars {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.distribution-bar {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.bar-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  min-width: 80rpx;
}

.bar-container {
  flex: 1;
  height: 20rpx;
  background: $bg-color-light;
  border-radius: 10rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: $primary-color;
  transition: width $transition-normal;
}

.bar-count {
  font-size: $font-size-sm;
  color: $text-color-primary;
  min-width: 40rpx;
  text-align: right;
}
</style>
