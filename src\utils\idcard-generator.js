/**
 * 身份证号生成器工具类
 * 符合国标GB11643-1999规范
 */

class IdCardGenerator {
  constructor() {
    // 地区代码映射表（部分主要城市）
    this.areaCodes = {
      '北京市': {
        '110000': '北京市',
        '110100': '市辖区',
        '110101': '东城区',
        '110102': '西城区',
        '110105': '朝阳区',
        '110106': '丰台区',
        '110107': '石景山区',
        '110108': '海淀区',
        '110109': '门头沟区',
        '110111': '房山区',
        '110112': '通州区',
        '110113': '顺义区',
        '110114': '昌平区',
        '110115': '大兴区',
        '110116': '怀柔区',
        '110117': '平谷区',
        '110118': '密云区',
        '110119': '延庆区'
      },
      '上海市': {
        '310000': '上海市',
        '310100': '市辖区',
        '310101': '黄浦区',
        '310104': '徐汇区',
        '310105': '长宁区',
        '310106': '静安区',
        '310107': '普陀区',
        '310109': '虹口区',
        '310110': '杨浦区',
        '310112': '闵行区',
        '310113': '宝山区',
        '310114': '嘉定区',
        '310115': '浦东新区',
        '310116': '金山区',
        '310117': '松江区',
        '310118': '青浦区',
        '310120': '奉贤区',
        '310151': '崇明区'
      },
      '广东省': {
        '440000': '广东省',
        '440100': '广州市',
        '440103': '荔湾区',
        '440104': '越秀区',
        '440105': '海珠区',
        '440106': '天河区',
        '440111': '白云区',
        '440112': '黄埔区',
        '440113': '番禺区',
        '440114': '花都区',
        '440115': '南沙区',
        '440117': '从化区',
        '440118': '增城区',
        '440300': '深圳市',
        '440303': '罗湖区',
        '440304': '福田区',
        '440305': '南山区',
        '440306': '宝安区',
        '440307': '龙岗区',
        '440308': '盐田区',
        '440309': '龙华区',
        '440310': '坪山区',
        '440311': '光明区'
      },
      '江苏省': {
        '320000': '江苏省',
        '320100': '南京市',
        '320102': '玄武区',
        '320104': '秦淮区',
        '320105': '建邺区',
        '320106': '鼓楼区',
        '320111': '浦口区',
        '320113': '栖霞区',
        '320114': '雨花台区',
        '320115': '江宁区',
        '320116': '六合区',
        '320117': '溧水区',
        '320118': '高淳区',
        '320200': '无锡市',
        '320205': '锡山区',
        '320206': '惠山区',
        '320211': '滨湖区',
        '320213': '梁溪区',
        '320214': '新吴区'
      },
      '浙江省': {
        '330000': '浙江省',
        '330100': '杭州市',
        '330102': '上城区',
        '330105': '拱墅区',
        '330106': '西湖区',
        '330108': '滨江区',
        '330109': '萧山区',
        '330110': '余杭区',
        '330111': '富阳区',
        '330112': '临安区',
        '330113': '临平区',
        '330114': '钱塘区',
        '330200': '宁波市',
        '330203': '海曙区',
        '330204': '江东区',
        '330205': '江北区',
        '330206': '北仑区',
        '330211': '镇海区',
        '330212': '鄞州区'
      }
    }
    
    // 权重因子
    this.weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    
    // 校验码对应表
    this.checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
  }
  
  /**
   * 获取所有省份
   */
  getProvinces() {
    return Object.keys(this.areaCodes)
  }
  
  /**
   * 获取省份下的地区
   */
  getAreas(province) {
    return this.areaCodes[province] || {}
  }
  
  /**
   * 获取随机地区代码
   */
  getRandomAreaCode(province = null) {
    let areas = {}
    
    if (province && this.areaCodes[province]) {
      areas = this.areaCodes[province]
    } else {
      // 随机选择省份
      const provinces = Object.keys(this.areaCodes)
      const randomProvince = provinces[Math.floor(Math.random() * provinces.length)]
      areas = this.areaCodes[randomProvince]
    }
    
    const areaCodes = Object.keys(areas).filter(code => code.length === 6)
    return areaCodes[Math.floor(Math.random() * areaCodes.length)]
  }
  
  /**
   * 生成随机出生日期
   */
  generateRandomBirthDate(startYear = 1950, endYear = 2005) {
    const year = Math.floor(Math.random() * (endYear - startYear + 1)) + startYear
    const month = Math.floor(Math.random() * 12) + 1
    const daysInMonth = this.getDaysInMonth(year, month)
    const day = Math.floor(Math.random() * daysInMonth) + 1
    
    return {
      year,
      month: month.toString().padStart(2, '0'),
      day: day.toString().padStart(2, '0'),
      dateString: `${year}${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}`
    }
  }
  
  /**
   * 获取指定年月的天数
   */
  getDaysInMonth(year, month) {
    return new Date(year, month, 0).getDate()
  }
  
  /**
   * 生成随机顺序码
   */
  generateRandomSequence() {
    return Math.floor(Math.random() * 999) + 1
  }
  
  /**
   * 计算校验码
   */
  calculateCheckCode(idCard17) {
    let sum = 0
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard17[i]) * this.weights[i]
    }
    const remainder = sum % 11
    return this.checkCodes[remainder]
  }
  
  /**
   * 生成单个身份证号
   */
  generateSingle(options = {}) {
    const {
      province = null,
      areaCode = null,
      gender = null, // 'male', 'female', null(随机)
      startYear = 1950,
      endYear = 2005,
      birthDate = null
    } = options
    
    // 地区代码
    const area = areaCode || this.getRandomAreaCode(province)
    
    // 出生日期
    const birth = birthDate || this.generateRandomBirthDate(startYear, endYear)
    
    // 顺序码（第15-17位）
    let sequence = this.generateRandomSequence()
    
    // 根据性别调整顺序码（第17位奇数为男，偶数为女）
    if (gender === 'male' && sequence % 2 === 0) {
      sequence = sequence === 999 ? 998 : sequence + 1
    } else if (gender === 'female' && sequence % 2 === 1) {
      sequence = sequence === 999 ? 998 : sequence + 1
    }
    
    const sequenceStr = sequence.toString().padStart(3, '0')
    
    // 前17位
    const idCard17 = area + birth.dateString + sequenceStr
    
    // 计算校验码
    const checkCode = this.calculateCheckCode(idCard17)
    
    // 完整身份证号
    const idCard = idCard17 + checkCode
    
    return {
      idCard,
      info: {
        areaCode: area,
        areaName: this.getAreaName(area),
        birthDate: `${birth.year}-${birth.month}-${birth.day}`,
        age: this.calculateAge(birth.year),
        gender: parseInt(sequenceStr[2]) % 2 === 1 ? '男' : '女',
        sequence: sequenceStr,
        checkCode
      }
    }
  }
  
  /**
   * 批量生成身份证号
   */
  generateBatch(count, options = {}) {
    const results = []
    for (let i = 0; i < count; i++) {
      results.push(this.generateSingle(options))
    }
    return results
  }
  
  /**
   * 验证身份证号
   */
  validate(idCard) {
    if (!idCard || typeof idCard !== 'string') {
      return { valid: false, error: '身份证号不能为空' }
    }
    
    // 长度检查
    if (idCard.length !== 18) {
      return { valid: false, error: '身份证号长度必须为18位' }
    }
    
    // 格式检查
    const pattern = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/
    if (!pattern.test(idCard)) {
      return { valid: false, error: '身份证号格式不正确' }
    }
    
    // 地区代码检查
    const areaCode = idCard.substring(0, 6)
    if (!this.isValidAreaCode(areaCode)) {
      return { valid: false, error: '地区代码不存在' }
    }
    
    // 出生日期检查
    const birthDate = idCard.substring(6, 14)
    if (!this.isValidBirthDate(birthDate)) {
      return { valid: false, error: '出生日期不正确' }
    }
    
    // 校验码检查
    const idCard17 = idCard.substring(0, 17)
    const checkCode = this.calculateCheckCode(idCard17)
    if (checkCode.toUpperCase() !== idCard[17].toUpperCase()) {
      return { valid: false, error: '校验码不正确' }
    }
    
    return { 
      valid: true, 
      info: this.parseIdCard(idCard)
    }
  }
  
  /**
   * 解析身份证号信息
   */
  parseIdCard(idCard) {
    if (!this.validate(idCard).valid) {
      return null
    }
    
    const areaCode = idCard.substring(0, 6)
    const birthDate = idCard.substring(6, 14)
    const sequence = idCard.substring(14, 17)
    const checkCode = idCard[17]
    
    const year = parseInt(birthDate.substring(0, 4))
    const month = birthDate.substring(4, 6)
    const day = birthDate.substring(6, 8)
    
    return {
      areaCode,
      areaName: this.getAreaName(areaCode),
      birthDate: `${year}-${month}-${day}`,
      age: this.calculateAge(year),
      gender: parseInt(sequence[2]) % 2 === 1 ? '男' : '女',
      sequence,
      checkCode
    }
  }
  
  /**
   * 检查地区代码是否有效
   */
  isValidAreaCode(areaCode) {
    for (const province in this.areaCodes) {
      if (this.areaCodes[province][areaCode]) {
        return true
      }
    }
    return false
  }
  
  /**
   * 检查出生日期是否有效
   */
  isValidBirthDate(birthDate) {
    const year = parseInt(birthDate.substring(0, 4))
    const month = parseInt(birthDate.substring(4, 6))
    const day = parseInt(birthDate.substring(6, 8))
    
    // 年份范围检查
    if (year < 1900 || year > new Date().getFullYear()) {
      return false
    }
    
    // 月份检查
    if (month < 1 || month > 12) {
      return false
    }
    
    // 日期检查
    const daysInMonth = this.getDaysInMonth(year, month)
    if (day < 1 || day > daysInMonth) {
      return false
    }
    
    return true
  }
  
  /**
   * 获取地区名称
   */
  getAreaName(areaCode) {
    for (const province in this.areaCodes) {
      if (this.areaCodes[province][areaCode]) {
        return this.areaCodes[province][areaCode]
      }
    }
    return '未知地区'
  }
  
  /**
   * 计算年龄
   */
  calculateAge(birthYear) {
    const currentYear = new Date().getFullYear()
    return currentYear - birthYear
  }
  
  /**
   * 格式化身份证号显示
   */
  formatIdCard(idCard) {
    if (!idCard || idCard.length !== 18) {
      return idCard
    }
    return `${idCard.substring(0, 6)} ${idCard.substring(6, 14)} ${idCard.substring(14)}`
  }
  
  /**
   * 导出为CSV格式
   */
  exportToCSV(idCards) {
    const headers = ['身份证号', '地区代码', '地区名称', '出生日期', '年龄', '性别', '顺序码', '校验码']
    const rows = [headers.join(',')]
    
    idCards.forEach(item => {
      const { idCard, info } = item
      const row = [
        idCard,
        info.areaCode,
        info.areaName,
        info.birthDate,
        info.age,
        info.gender,
        info.sequence,
        info.checkCode
      ]
      rows.push(row.join(','))
    })
    
    return rows.join('\n')
  }
  
  /**
   * 导出为JSON格式
   */
  exportToJSON(idCards) {
    return JSON.stringify(idCards, null, 2)
  }
  
  /**
   * 获取统计信息
   */
  getStatistics(idCards) {
    const stats = {
      total: idCards.length,
      genderCount: { male: 0, female: 0 },
      ageGroups: {
        '18-30': 0,
        '31-40': 0,
        '41-50': 0,
        '51-60': 0,
        '60+': 0
      },
      areaCount: {}
    }
    
    idCards.forEach(item => {
      const { info } = item
      
      // 性别统计
      if (info.gender === '男') {
        stats.genderCount.male++
      } else {
        stats.genderCount.female++
      }
      
      // 年龄组统计
      const age = info.age
      if (age >= 18 && age <= 30) {
        stats.ageGroups['18-30']++
      } else if (age >= 31 && age <= 40) {
        stats.ageGroups['31-40']++
      } else if (age >= 41 && age <= 50) {
        stats.ageGroups['41-50']++
      } else if (age >= 51 && age <= 60) {
        stats.ageGroups['51-60']++
      } else if (age > 60) {
        stats.ageGroups['60+']++
      }
      
      // 地区统计
      const areaName = info.areaName
      stats.areaCount[areaName] = (stats.areaCount[areaName] || 0) + 1
    })
    
    return stats
  }
}

// 创建全局实例
const idCardGenerator = new IdCardGenerator()

export default idCardGenerator
