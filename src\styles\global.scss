/* 全局样式 */
page {
  background-color: $bg-color-secondary;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: $line-height-normal;
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $secondary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-danger {
  color: $danger-color;
}

.bg-primary {
  background-color: $primary-color;
}

.bg-gradient-primary {
  background: $gradient-primary;
}

.shadow-md {
  box-shadow: $shadow-md;
}

.rounded-md {
  border-radius: $border-radius-md;
}

.rounded-lg {
  border-radius: $border-radius-lg;
}

.p-sm {
  padding: $spacing-sm;
}

.p-md {
  padding: $spacing-md;
}

.m-sm {
  margin: $spacing-sm;
}

.m-md {
  margin: $spacing-md;
}

/* 卡片样式 */
.card {
  background-color: $bg-color-primary;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  padding: $spacing-md;
  margin: $spacing-sm;
}

.card-gradient {
  background: $gradient-primary;
  color: $text-color-white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  padding: $spacing-md;
  margin: $spacing-sm;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  font-weight: $font-weight-medium;
  transition: all $transition-normal;
  border: none;
  cursor: pointer;
  font-size: $font-size-md;
}

.btn-primary {
  background: $gradient-primary;
  color: $text-color-white;
}

.btn-secondary {
  background: $gradient-secondary;
  color: $text-color-white;
}

.btn-success {
  background: $gradient-success;
  color: $text-color-white;
}

.btn-warning {
  background: $gradient-warning;
  color: $text-color-primary;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid $primary-color;
  color: $primary-color;
}

/* 输入框样式 */
.input {
  padding: $spacing-sm;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  transition: border-color $transition-normal;
}

.input:focus {
  border-color: $primary-color;
  outline: none;
}

/* 标题样式 */
.title-lg {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  margin-bottom: $spacing-md;
}

.title-md {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
}

.title-sm {
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: $text-color-secondary;
  margin-bottom: $spacing-sm;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: $spacing-md;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 动画类 */
.fade-in {
  animation: fadeIn $transition-normal ease-in-out;
}

.slide-up {
  animation: slideUp $transition-normal ease-out;
}

.bounce {
  animation: bounce $transition-slow ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

/* 响应式工具类 */
.container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 状态样式 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.hidden {
  display: none;
}

.visible {
  display: block;
}
