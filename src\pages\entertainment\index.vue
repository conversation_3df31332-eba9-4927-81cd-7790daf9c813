<template>
  <view class="page-container">
    <!-- 头部区域 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content safe-area-inset-top">
        <text class="page-title">娱乐游戏</text>
        <text class="page-desc">经典棋牌游戏，智能AI对战</text>
      </view>
    </view>
    
    <!-- 游戏列表 -->
    <view class="games-section">
      <view class="games-grid">
        <view 
          v-for="game in games" 
          :key="game.id"
          class="game-card"
          @tap="goToGame(game)"
        >
          <view class="game-bg" :style="{ background: game.gradient }"></view>
          <view class="game-content">
            <view class="game-icon">{{ game.icon }}</view>
            <text class="game-name">{{ game.name }}</text>
            <text class="game-desc">{{ game.desc }}</text>
            <view class="game-features">
              <text 
                v-for="feature in game.features" 
                :key="feature"
                class="feature-tag"
              >
                {{ feature }}
              </text>
            </view>
          </view>
          <view class="game-status" v-if="game.status">
            <text class="status-text">{{ game.status }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 游戏统计 -->
    <view class="stats-section">
      <view class="section-title">
        <text class="title-text">游戏统计</text>
        <text class="title-desc">您的游戏成就</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-card card">
          <view class="stat-icon">🎮</view>
          <view class="stat-info">
            <text class="stat-value">{{ gameStats.totalGames }}</text>
            <text class="stat-label">总局数</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">🏆</view>
          <view class="stat-info">
            <text class="stat-value">{{ gameStats.winGames }}</text>
            <text class="stat-label">胜利</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">📊</view>
          <view class="stat-info">
            <text class="stat-value">{{ winRate }}%</text>
            <text class="stat-label">胜率</text>
          </view>
        </view>
        
        <view class="stat-card card">
          <view class="stat-icon">⏱️</view>
          <view class="stat-info">
            <text class="stat-value">{{ formatTime(gameStats.totalTime) }}</text>
            <text class="stat-label">游戏时长</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 最近对局 -->
    <view class="recent-section" v-if="recentGames.length > 0">
      <view class="section-title">
        <text class="title-text">最近对局</text>
        <text class="title-desc">查看最近的游戏记录</text>
      </view>
      
      <view class="recent-list">
        <view 
          v-for="game in recentGames" 
          :key="game.id"
          class="recent-item card"
        >
          <view class="game-info">
            <text class="game-name">{{ game.gameName }}</text>
            <text class="game-time">{{ formatDateTime(game.timestamp) }}</text>
          </view>
          <view class="game-result" :class="{ win: game.result === 'win' }">
            <text class="result-text">{{ game.result === 'win' ? '胜利' : '失败' }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 游戏规则说明 -->
    <view class="rules-section">
      <view class="section-title">
        <text class="title-text">游戏说明</text>
        <text class="title-desc">了解游戏规则和操作方法</text>
      </view>
      
      <view class="rules-list">
        <view class="rule-item card" @tap="showRules('chess')">
          <view class="rule-icon">♟️</view>
          <view class="rule-info">
            <text class="rule-title">中国象棋规则</text>
            <text class="rule-desc">了解象棋基本规则和走法</text>
          </view>
          <view class="rule-arrow">></view>
        </view>
        
        <view class="rule-item card" @tap="showRules('gobang')">
          <view class="rule-icon">⚫</view>
          <view class="rule-info">
            <text class="rule-title">五子棋规则</text>
            <text class="rule-desc">五子连珠的获胜条件</text>
          </view>
          <view class="rule-arrow">></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRedPacketStore } from '../../stores/redpacket.js'
import audioManager from '../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 游戏配置
const games = ref([
  {
    id: 'chess',
    name: '中国象棋',
    desc: '经典象棋对弈',
    icon: '♟️',
    path: '/pages/entertainment/chess/index',
    gradient: 'linear-gradient(135deg, #8b5a2b 0%, #d2691e 100%)',
    features: ['AI对战', '悔棋', '音效'],
    status: '完整版'
  },
  {
    id: 'gobang',
    name: '五子棋',
    desc: '五子连珠获胜',
    icon: '⚫',
    path: '/pages/entertainment/gobang/index',
    gradient: 'linear-gradient(135deg, #4a5568 0%, #718096 100%)',
    features: ['智能AI', '多难度', '统计'],
    status: '完整版'
  },
  {
    id: 'weiqi',
    name: '围棋',
    desc: '围地制胜',
    icon: '⚪',
    path: '/pages/entertainment/weiqi/index',
    gradient: 'linear-gradient(135deg, #2d3748 0%, #4a5568 100%)',
    features: ['基础版', '学习模式'],
    status: '开发中'
  },
  {
    id: 'xiaoxiaole',
    name: '消消乐',
    desc: '休闲益智',
    icon: '🍭',
    path: '/pages/entertainment/xiaoxiaole/index',
    gradient: 'linear-gradient(135deg, #9f7aea 0%, #d53f8c 100%)',
    features: ['关卡模式', '道具系统'],
    status: '开发中'
  }
])

// 游戏统计数据
const gameStats = ref({
  totalGames: 0,
  winGames: 0,
  totalTime: 0
})

// 最近游戏记录
const recentGames = ref([])

// 计算属性
const winRate = computed(() => {
  if (gameStats.value.totalGames === 0) return 0
  return Math.round((gameStats.value.winGames / gameStats.value.totalGames) * 100)
})

// 页面加载
onMounted(() => {
  loadGameStats()
  loadRecentGames()
  audioManager.playSound('page_turn')
})

// 加载游戏统计
const loadGameStats = () => {
  try {
    const stats = uni.getStorageSync('game_stats')
    if (stats) {
      gameStats.value = { ...gameStats.value, ...stats }
    }
  } catch (error) {
    console.warn('加载游戏统计失败:', error)
  }
}

// 加载最近游戏
const loadRecentGames = () => {
  try {
    const recent = uni.getStorageSync('recent_games')
    if (recent && Array.isArray(recent)) {
      recentGames.value = recent.slice(0, 5) // 只显示最近5局
    }
  } catch (error) {
    console.warn('加载最近游戏失败:', error)
  }
}

// 跳转到游戏
const goToGame = (game) => {
  audioManager.playSound('button_click')
  
  if (game.status === '开发中') {
    uni.showToast({
      title: '游戏开发中，敬请期待',
      icon: 'none'
    })
    return
  }
  
  // 触发游戏统计
  redPacketStore.onGamePlayed()
  
  uni.navigateTo({
    url: game.path
  })
}

// 显示游戏规则
const showRules = (gameType) => {
  audioManager.playSound('click')
  
  const rules = {
    chess: {
      title: '中国象棋规则',
      content: `
1. 棋盘：9条直线，10条横线组成
2. 棋子：红黑双方各16枚棋子
3. 走法：每种棋子有特定的走法规则
4. 胜负：将死对方将帅即获胜
5. 特殊规则：将帅不能照面等
      `
    },
    gobang: {
      title: '五子棋规则',
      content: `
1. 棋盘：15×15线交叉点
2. 棋子：黑白双方轮流下子
3. 胜负：率先形成五子连珠者获胜
4. 连珠：横、竖、斜任意方向
5. 禁手：黑棋有三三、四四等禁手
      `
    }
  }
  
  const rule = rules[gameType]
  if (rule) {
    uni.showModal({
      title: rule.title,
      content: rule.content,
      showCancel: false,
      confirmText: '知道了'
    })
  }
}

// 格式化时间
const formatTime = (seconds) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
  return `${Math.floor(seconds / 3600)}小时`
}

// 格式化日期时间
const formatDateTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 86400000) { // 24小时内
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  } else {
    return date.toLocaleDateString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit' 
    })
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

.header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header-content {
  position: relative;
  height: 100%;
  padding: 40rpx $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  font-size: $font-size-md;
  opacity: 0.9;
}

.games-section, .stats-section, .recent-section, .rules-section {
  padding: $spacing-md;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
  margin-top: -60rpx;
  position: relative;
  z-index: 1;
}

.game-card {
  position: relative;
  height: 280rpx;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-lg;
}

.game-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.game-content {
  position: relative;
  height: 100%;
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.game-icon {
  font-size: 80rpx;
  margin-bottom: $spacing-sm;
}

.game-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  margin-bottom: 5rpx;
  display: block;
}

.game-desc {
  font-size: $font-size-sm;
  opacity: 0.9;
  margin-bottom: $spacing-sm;
  display: block;
}

.game-features {
  display: flex;
  flex-wrap: wrap;
  gap: 5rpx;
  justify-content: center;
}

.feature-tag {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  backdrop-filter: blur(5px);
}

.game-status {
  position: absolute;
  top: $spacing-sm;
  right: $spacing-sm;
  padding: 4rpx 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  backdrop-filter: blur(5px);
}

.status-text {
  font-size: 20rpx;
  color: white;
  font-weight: $font-weight-bold;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.title-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  gap: $spacing-md;
}

.stat-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.recent-list, .rules-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
}

.game-info {
  flex: 1;
}

.game-name {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.game-time {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.game-result {
  padding: 4rpx 12rpx;
  border-radius: $border-radius-sm;
  background: $bg-color-light;
  
  &.win {
    background: $success-color;
  }
}

.result-text {
  font-size: $font-size-xs;
  color: $text-color-secondary;
  
  .win & {
    color: white;
  }
}

.rule-item {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  gap: $spacing-md;
}

.rule-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.rule-info {
  flex: 1;
}

.rule-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.rule-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.rule-arrow {
  font-size: $font-size-lg;
  color: $text-color-light;
}
</style>
