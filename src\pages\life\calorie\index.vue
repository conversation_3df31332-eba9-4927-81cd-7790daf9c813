<template>
  <view class="calorie-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="page-title">卡路里计算</text>
        <text class="page-desc">每日热量需求计算</text>
      </view>
    </view>
    
    <!-- 开发中提示 -->
    <view class="development-notice">
      <view class="notice-card">
        <view class="notice-icon">🔥</view>
        <text class="notice-title">功能规划中</text>
        <text class="notice-desc">卡路里计算器正在规划设计中，即将开始开发！</text>
        
        <view class="features-preview">
          <text class="features-title">计划功能：</text>
          <view class="feature-list">
            <view class="feature-item">
              <text class="feature-icon">👤</text>
              <text class="feature-text">基础信息录入</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🏃</text>
              <text class="feature-text">活动量评估</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🎯</text>
              <text class="feature-text">目标设定</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📊</text>
              <text class="feature-text">热量计算</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🍎</text>
              <text class="feature-text">食物热量查询</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">📈</text>
              <text class="feature-text">摄入记录</text>
            </view>
          </view>
        </view>
        
        <view class="progress-section">
          <text class="progress-title">开发进度</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: '10%' }"></view>
          </view>
          <text class="progress-text">10% 完成</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="actions">
      <button class="action-btn primary" @tap="goBack">
        <text class="btn-text">返回生活助手</text>
      </button>
      
      <button class="action-btn" @tap="showNotification">
        <text class="btn-text">开发完成通知我</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import audioManager from '../../../utils/audio.js'

// 返回生活助手
const goBack = () => {
  audioManager.playSound('button_click')
  uni.navigateBack()
}

// 显示通知设置
const showNotification = () => {
  audioManager.playSound('click')
  uni.showModal({
    title: '开发通知',
    content: '我们会在卡路里计算器功能开发完成后第一时间通知您！',
    showCancel: false,
    confirmText: '好的'
  })
}
</script>

<style lang="scss" scoped>
.calorie-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.header {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.header-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  font-size: $font-size-md;
  opacity: 0.8;
}

.development-notice {
  padding: $spacing-md;
  margin-top: -40rpx;
  position: relative;
  z-index: 1;
}

.notice-card {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-lg;
  text-align: center;
}

.notice-icon {
  font-size: 120rpx;
  margin-bottom: $spacing-md;
}

.notice-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.notice-desc {
  font-size: $font-size-md;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-lg;
}

.features-preview {
  text-align: left;
  margin-bottom: $spacing-lg;
}

.features-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-md;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-sm;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: $bg-color-light;
  border-radius: $border-radius-md;
}

.feature-icon {
  font-size: $font-size-lg;
}

.feature-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.progress-section {
  text-align: center;
}

.progress-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: $bg-color-light;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: $spacing-sm;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  transition: width $transition-normal;
}

.progress-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.actions {
  padding: $spacing-md;
  display: flex;
  gap: $spacing-md;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  
  &.primary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: white;
  }
  
  &:not(.primary) {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2rpx solid rgba(255, 255, 255, 0.3);
  }
}
</style>
