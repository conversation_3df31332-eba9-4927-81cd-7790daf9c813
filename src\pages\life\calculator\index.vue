<template>
  <view class="calculator-container">
    <!-- 显示屏 -->
    <view class="display-section">
      <view class="display-card">
        <!-- 历史表达式 -->
        <view class="history-display" v-if="historyExpression">
          <text class="history-text">{{ historyExpression }}</text>
        </view>
        
        <!-- 当前表达式 -->
        <view class="current-display">
          <text class="current-text">{{ currentExpression || '0' }}</text>
        </view>
        
        <!-- 结果显示 -->
        <view class="result-display" v-if="result !== null">
          <text class="result-text">= {{ result }}</text>
        </view>
        
        <!-- 状态指示器 -->
        <view class="status-indicators">
          <text class="angle-mode" :class="{ active: angleMode === 'deg' }">DEG</text>
          <text class="angle-mode" :class="{ active: angleMode === 'rad' }">RAD</text>
          <text class="memory-indicator" v-if="hasMemory">M</text>
        </view>
      </view>
    </view>
    
    <!-- 功能按钮区 -->
    <view class="function-section">
      <view class="function-buttons">
        <button class="function-btn" @tap="toggleAngleMode">
          <text class="btn-text">{{ angleMode.toUpperCase() }}</text>
        </button>
        <button class="function-btn" @tap="showHistory">
          <text class="btn-text">历史</text>
        </button>
        <button class="function-btn" @tap="showFunctions">
          <text class="btn-text">函数</text>
        </button>
        <button class="function-btn" @tap="clearAll">
          <text class="btn-text">清空</text>
        </button>
      </view>
    </view>
    
    <!-- 科学函数区 -->
    <view class="scientific-section" v-if="showScientific">
      <scroll-view class="scientific-scroll" scroll-x>
        <view class="scientific-buttons">
          <button 
            v-for="func in scientificFunctions" 
            :key="func.key"
            class="scientific-btn"
            @tap="inputFunction(func.key)"
          >
            <text class="btn-text">{{ func.label }}</text>
          </button>
        </view>
      </scroll-view>
    </view>
    
    <!-- 主键盘区 -->
    <view class="keyboard-section">
      <view class="keyboard-grid">
        <!-- 第一行 -->
        <button class="keyboard-btn memory" @tap="memoryClear">MC</button>
        <button class="keyboard-btn memory" @tap="memoryRecall">MR</button>
        <button class="keyboard-btn memory" @tap="memoryAdd">M+</button>
        <button class="keyboard-btn memory" @tap="memorySubtract">M-</button>
        
        <!-- 第二行 -->
        <button class="keyboard-btn operator" @tap="inputOperator('(')">(</button>
        <button class="keyboard-btn operator" @tap="inputOperator(')')">)</button>
        <button class="keyboard-btn operator" @tap="inputOperator('%')">%</button>
        <button class="keyboard-btn operator clear" @tap="backspace">⌫</button>
        
        <!-- 第三行 -->
        <button class="keyboard-btn number" @tap="inputNumber('7')">7</button>
        <button class="keyboard-btn number" @tap="inputNumber('8')">8</button>
        <button class="keyboard-btn number" @tap="inputNumber('9')">9</button>
        <button class="keyboard-btn operator" @tap="inputOperator('/')">÷</button>
        
        <!-- 第四行 -->
        <button class="keyboard-btn number" @tap="inputNumber('4')">4</button>
        <button class="keyboard-btn number" @tap="inputNumber('5')">5</button>
        <button class="keyboard-btn number" @tap="inputNumber('6')">6</button>
        <button class="keyboard-btn operator" @tap="inputOperator('*')">×</button>
        
        <!-- 第五行 -->
        <button class="keyboard-btn number" @tap="inputNumber('1')">1</button>
        <button class="keyboard-btn number" @tap="inputNumber('2')">2</button>
        <button class="keyboard-btn number" @tap="inputNumber('3')">3</button>
        <button class="keyboard-btn operator" @tap="inputOperator('-')">-</button>
        
        <!-- 第六行 -->
        <button class="keyboard-btn number zero" @tap="inputNumber('0')">0</button>
        <button class="keyboard-btn number" @tap="inputNumber('.')">.</button>
        <button class="keyboard-btn operator" @tap="inputOperator('+')">+</button>
        
        <!-- 等号 -->
        <button class="keyboard-btn equals" @tap="calculate" style="grid-row: span 2;">=</button>
      </view>
    </view>
    
    <!-- 历史记录弹窗 -->
    <view v-if="showHistoryModal" class="history-modal" @tap="closeHistory">
      <view class="modal-content" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">计算历史</text>
          <button class="close-btn" @tap="closeHistory">×</button>
        </view>
        
        <scroll-view class="history-list" scroll-y>
          <view 
            v-for="(item, index) in calculatorHistory" 
            :key="index"
            class="history-item"
            @tap="useHistoryItem(item)"
          >
            <text class="history-expression">{{ item.expression }}</text>
            <text class="history-result">= {{ item.result }}</text>
            <text class="history-time">{{ formatTime(item.timestamp) }}</text>
          </view>
          
          <view v-if="calculatorHistory.length === 0" class="empty-history">
            <text class="empty-text">暂无计算历史</text>
          </view>
        </scroll-view>
        
        <button class="clear-history-btn" @tap="clearHistory">清空历史</button>
      </view>
    </view>
    
    <!-- 函数列表弹窗 -->
    <view v-if="showFunctionsModal" class="functions-modal" @tap="closeFunctions">
      <view class="modal-content" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">科学函数</text>
          <button class="close-btn" @tap="closeFunctions">×</button>
        </view>
        
        <view class="functions-grid">
          <button 
            v-for="func in allFunctions" 
            :key="func.key"
            class="function-item"
            @tap="inputFunction(func.key)"
          >
            <text class="function-name">{{ func.label }}</text>
            <text class="function-desc">{{ func.desc }}</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRedPacketStore } from '../../../stores/redpacket.js'
import calculator from '../../../utils/calculator.js'
import audioManager from '../../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 显示状态
const currentExpression = ref('')
const historyExpression = ref('')
const result = ref(null)
const showScientific = ref(false)
const showHistoryModal = ref(false)
const showFunctionsModal = ref(false)

// 计算器状态
const angleMode = ref('deg')
const hasMemory = ref(false)
const calculatorHistory = ref([])

// 科学函数
const scientificFunctions = ref([
  { key: 'sin', label: 'sin' },
  { key: 'cos', label: 'cos' },
  { key: 'tan', label: 'tan' },
  { key: 'log', label: 'log' },
  { key: 'ln', label: 'ln' },
  { key: 'sqrt', label: '√' },
  { key: '^', label: 'x^y' },
  { key: 'π', label: 'π' },
  { key: 'e', label: 'e' }
])

// 所有函数
const allFunctions = ref([
  { key: 'sin', label: 'sin', desc: '正弦函数' },
  { key: 'cos', label: 'cos', desc: '余弦函数' },
  { key: 'tan', label: 'tan', desc: '正切函数' },
  { key: 'asin', label: 'asin', desc: '反正弦函数' },
  { key: 'acos', label: 'acos', desc: '反余弦函数' },
  { key: 'atan', label: 'atan', desc: '反正切函数' },
  { key: 'sinh', label: 'sinh', desc: '双曲正弦' },
  { key: 'cosh', label: 'cosh', desc: '双曲余弦' },
  { key: 'tanh', label: 'tanh', desc: '双曲正切' },
  { key: 'log', label: 'log', desc: '常用对数' },
  { key: 'ln', label: 'ln', desc: '自然对数' },
  { key: 'sqrt', label: 'sqrt', desc: '平方根' },
  { key: 'abs', label: 'abs', desc: '绝对值' },
  { key: 'ceil', label: 'ceil', desc: '向上取整' },
  { key: 'floor', label: 'floor', desc: '向下取整' },
  { key: 'round', label: 'round', desc: '四舍五入' },
  { key: 'exp', label: 'exp', desc: '指数函数' }
])

// 页面加载
onMounted(() => {
  loadCalculatorState()
  audioManager.playSound('page_turn')
})

// 加载计算器状态
const loadCalculatorState = () => {
  try {
    const state = uni.getStorageSync('calculator_state')
    if (state) {
      angleMode.value = state.angleMode || 'deg'
      hasMemory.value = state.hasMemory || false
      calculator.setAngleMode(angleMode.value)
      if (state.memory) {
        calculator.memoryStore(state.memory)
      }
    }
    
    const history = uni.getStorageSync('calculator_history')
    if (history && Array.isArray(history)) {
      calculatorHistory.value = history
      calculator.history = history
    }
  } catch (error) {
    console.warn('加载计算器状态失败:', error)
  }
}

// 保存计算器状态
const saveCalculatorState = () => {
  try {
    const state = {
      angleMode: angleMode.value,
      hasMemory: hasMemory.value,
      memory: calculator.getMemory()
    }
    uni.setStorageSync('calculator_state', state)
    uni.setStorageSync('calculator_history', calculator.getHistory())
  } catch (error) {
    console.warn('保存计算器状态失败:', error)
  }
}

// 输入数字
const inputNumber = (num) => {
  if (result.value !== null && !currentExpression.value) {
    // 如果刚计算完结果，清空开始新计算
    clearAll()
  }
  
  currentExpression.value += num
  result.value = null
  audioManager.playSound('click')
}

// 输入运算符
const inputOperator = (op) => {
  if (!currentExpression.value && op !== '(' && op !== '-') {
    return
  }
  
  // 处理显示符号
  let displayOp = op
  if (op === '*') displayOp = '×'
  if (op === '/') displayOp = '÷'
  
  currentExpression.value += displayOp
  result.value = null
  audioManager.playSound('click')
}

// 输入函数
const inputFunction = (func) => {
  if (func === 'π' || func === 'e') {
    currentExpression.value += func
  } else if (func === '^') {
    currentExpression.value += '^'
  } else {
    currentExpression.value += func + '('
  }
  
  result.value = null
  audioManager.playSound('click')
  
  // 关闭函数弹窗
  if (showFunctionsModal.value) {
    closeFunctions()
  }
}

// 计算
const calculate = () => {
  if (!currentExpression.value) return
  
  // 处理显示符号转换
  let expression = currentExpression.value
  expression = expression.replace(/×/g, '*').replace(/÷/g, '/')
  
  const calcResult = calculator.calculate(expression)
  
  if (calcResult.success) {
    historyExpression.value = currentExpression.value
    result.value = calcResult.result
    calculatorHistory.value = calculator.getHistory()
    
    // 触发工具使用统计
    redPacketStore.onToolUsed()
    
    audioManager.playSound('success')
    saveCalculatorState()
  } else {
    audioManager.playSound('error')
    uni.showToast({
      title: calcResult.error,
      icon: 'none'
    })
  }
}

// 退格
const backspace = () => {
  if (currentExpression.value) {
    currentExpression.value = currentExpression.value.slice(0, -1)
    result.value = null
  }
  audioManager.playSound('click')
}

// 清空
const clearAll = () => {
  currentExpression.value = ''
  historyExpression.value = ''
  result.value = null
  audioManager.playSound('click')
}

// 切换角度模式
const toggleAngleMode = () => {
  angleMode.value = angleMode.value === 'deg' ? 'rad' : 'deg'
  calculator.setAngleMode(angleMode.value)
  audioManager.playSound('click')
  saveCalculatorState()
}

// 内存操作
const memoryClear = () => {
  calculator.memoryClear()
  hasMemory.value = false
  audioManager.playSound('click')
  saveCalculatorState()
}

const memoryRecall = () => {
  const memory = calculator.memoryRecall()
  if (memory !== 0) {
    currentExpression.value += memory.toString()
    result.value = null
  }
  audioManager.playSound('click')
}

const memoryAdd = () => {
  if (result.value !== null) {
    calculator.memoryAdd(parseFloat(result.value))
    hasMemory.value = true
    audioManager.playSound('success')
    saveCalculatorState()
  }
}

const memorySubtract = () => {
  if (result.value !== null) {
    calculator.memorySubtract(parseFloat(result.value))
    hasMemory.value = true
    audioManager.playSound('success')
    saveCalculatorState()
  }
}

// 显示历史记录
const showHistory = () => {
  showHistoryModal.value = true
  audioManager.playSound('click')
}

const closeHistory = () => {
  showHistoryModal.value = false
}

// 使用历史记录项
const useHistoryItem = (item) => {
  currentExpression.value = item.expression
  result.value = item.result
  historyExpression.value = ''
  closeHistory()
  audioManager.playSound('click')
}

// 清空历史记录
const clearHistory = () => {
  calculator.clearHistory()
  calculatorHistory.value = []
  saveCalculatorState()
  audioManager.playSound('click')
}

// 显示函数列表
const showFunctions = () => {
  showFunctionsModal.value = true
  audioManager.playSound('click')
}

const closeFunctions = () => {
  showFunctionsModal.value = false
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.calculator-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.display-section {
  padding: $spacing-md;
  flex-shrink: 0;
}

.display-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-lg;
  backdrop-filter: blur(10px);
}

.history-display {
  margin-bottom: $spacing-sm;
}

.history-text {
  font-size: $font-size-md;
  color: $text-color-secondary;
  font-family: 'Courier New', monospace;
}

.current-display {
  margin-bottom: $spacing-sm;
}

.current-text {
  font-size: $font-size-2xl;
  color: $text-color-primary;
  font-weight: $font-weight-medium;
  font-family: 'Courier New', monospace;
  word-break: break-all;
}

.result-display {
  margin-bottom: $spacing-sm;
}

.result-text {
  font-size: $font-size-xl;
  color: $primary-color;
  font-weight: $font-weight-bold;
  font-family: 'Courier New', monospace;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.angle-mode {
  font-size: $font-size-xs;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-sm;
  background: $bg-color-light;
  color: $text-color-light;
  
  &.active {
    background: $primary-color;
    color: white;
  }
}

.memory-indicator {
  font-size: $font-size-xs;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-sm;
  background: $warning-color;
  color: white;
  font-weight: $font-weight-bold;
}

.function-section {
  padding: 0 $spacing-md;
  flex-shrink: 0;
}

.function-buttons {
  display: flex;
  gap: $spacing-sm;
}

.function-btn {
  flex: 1;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: $border-radius-md;
  color: white;
  font-size: $font-size-sm;
  backdrop-filter: blur(5px);
}

.scientific-section {
  padding: $spacing-sm $spacing-md;
  flex-shrink: 0;
}

.scientific-scroll {
  white-space: nowrap;
}

.scientific-buttons {
  display: inline-flex;
  gap: $spacing-sm;
}

.scientific-btn {
  min-width: 120rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: $border-radius-md;
  color: white;
  font-size: $font-size-sm;
  backdrop-filter: blur(5px);
}

.keyboard-section {
  flex: 1;
  padding: $spacing-md;
}

.keyboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $spacing-sm;
  height: 100%;
}

.keyboard-btn {
  height: 100rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.number {
    background: rgba(255, 255, 255, 0.9);
    color: $text-color-primary;
  }
  
  &.operator {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(5px);
    
    &.clear {
      background: $danger-color;
    }
  }
  
  &.memory {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    font-size: $font-size-md;
    backdrop-filter: blur(5px);
  }
  
  &.equals {
    background: $primary-color;
    color: white;
    grid-row: span 2;
  }
  
  &.zero {
    grid-column: span 2;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.history-modal, .functions-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
}

.modal-content {
  background: white;
  border-radius: $border-radius-lg;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-lg;
  border-bottom: 1rpx solid $border-color-light;
}

.modal-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background: $bg-color-light;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-xl;
  color: $text-color-secondary;
}

.history-list {
  flex: 1;
  padding: $spacing-md;
}

.history-item {
  padding: $spacing-md;
  border-bottom: 1rpx solid $border-color-light;
  
  &:last-child {
    border-bottom: none;
  }
}

.history-expression {
  font-size: $font-size-md;
  color: $text-color-primary;
  font-family: 'Courier New', monospace;
  display: block;
  margin-bottom: 5rpx;
}

.history-result {
  font-size: $font-size-sm;
  color: $primary-color;
  font-family: 'Courier New', monospace;
  display: block;
  margin-bottom: 5rpx;
}

.history-time {
  font-size: $font-size-xs;
  color: $text-color-light;
  display: block;
}

.empty-history {
  text-align: center;
  padding: $spacing-xl;
}

.empty-text {
  font-size: $font-size-md;
  color: $text-color-light;
}

.clear-history-btn {
  margin: $spacing-md;
  height: 80rpx;
  background: $danger-color;
  color: white;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-sm;
  padding: $spacing-md;
  max-height: 60vh;
  overflow-y: auto;
}

.function-item {
  padding: $spacing-md;
  background: $bg-color-light;
  border-radius: $border-radius-md;
  text-align: left;
}

.function-name {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.function-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}
</style>
