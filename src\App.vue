<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { useRedPacketStore } from "./stores/redpacket.js";
import audioManager from "./utils/audio.js";

// 红包状态管理
const redPacketStore = useRedPacketStore();

onLaunch(() => {
  console.log("App Launch");

  // 初始化红包系统
  redPacketStore.initUserData();

  // 初始化音频管理器
  audioManager.init();

  // 播放背景音乐
  audioManager.playBackgroundMusic('main', { loop: true });
});

onShow(() => {
  console.log("App Show");

  // 恢复背景音乐
  audioManager.resumeBackgroundMusic();
});

onHide(() => {
  console.log("App Hide");

  // 暂停背景音乐
  audioManager.pauseBackgroundMusic();

  // 保存红包数据
  redPacketStore.saveData();
});
</script>

<style lang="scss">
/* 导入全局样式变量 */
@import './uni.scss';

/* 全局样式 */
page {
  background-color: $bg-color-secondary;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: $line-height-normal;
}

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 通用动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-normal;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform $transition-normal;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 通用布局类 */
.container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.page-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

/* 卡片样式 */
.card {
  background-color: $bg-color-primary;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  padding: $spacing-md;
  margin: $spacing-sm 0;
}

.card-gradient {
  background: $gradient-primary;
  color: $text-color-white;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  font-weight: $font-weight-medium;
  font-size: $font-size-md;
  border: none;
  transition: all $transition-normal;
  cursor: pointer;
}

.btn-primary {
  background: $gradient-primary;
  color: $text-color-white;
}

.btn-secondary {
  background: $gradient-secondary;
  color: $text-color-white;
}

.btn-success {
  background: $gradient-success;
  color: $text-color-white;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid $primary-color;
  color: $primary-color;
}

.btn:active {
  transform: scale(0.98);
}

/* 文本样式 */
.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $text-color-secondary;
}

.text-muted {
  color: $text-color-light;
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: $font-weight-bold;
}

/* 间距工具类 */
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }

.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }

.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }

.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }

/* Flex布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: $spacing-md;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}
</style>
