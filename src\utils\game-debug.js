/**
 * 游戏调试工具
 * 用于测试游戏引擎是否正常工作
 */

import ChessEngine from './chess-engine.js'
import GobangEngine from './gobang-engine.js'

class GameDebugger {
  constructor() {
    this.testResults = []
  }
  
  // 运行所有测试
  runAllTests() {
    console.log('🎮 开始游戏引擎测试...')
    
    this.testResults = []
    
    // 测试象棋引擎
    this.testChessEngine()
    
    // 测试五子棋引擎
    this.testGobangEngine()
    
    // 输出测试结果
    this.outputResults()
    
    return this.testResults
  }
  
  // 测试象棋引擎
  testChessEngine() {
    console.log('♟️ 测试象棋引擎...')
    
    try {
      const chess = new ChessEngine()
      
      // 测试初始化
      this.addTest('象棋引擎初始化', chess.board !== null, '棋盘初始化成功')
      
      // 测试棋子识别
      const piece = chess.board[9][4] // 红帅
      this.addTest('棋子识别', piece === 'K', `红帅位置正确: ${piece}`)
      
      // 测试走法生成
      const moves = chess.generateMoves('red')
      this.addTest('走法生成', moves.length > 0, `生成${moves.length}个走法`)
      
      // 测试走法执行
      const testMove = { from: [9, 4], to: [8, 4] } // 帅向前一步
      const isValid = chess.isValidMove(testMove)
      this.addTest('走法验证', isValid, '帅可以向前移动')
      
      // 测试AI
      const aiMove = chess.getAIMove('easy')
      this.addTest('AI走法', aiMove !== null, 'AI能够生成走法')
      
      console.log('✅ 象棋引擎测试完成')
    } catch (error) {
      this.addTest('象棋引擎', false, `错误: ${error.message}`)
      console.error('❌ 象棋引擎测试失败:', error)
    }
  }
  
  // 测试五子棋引擎
  testGobangEngine() {
    console.log('⚫ 测试五子棋引擎...')
    
    try {
      const gobang = new GobangEngine()
      
      // 测试初始化
      this.addTest('五子棋引擎初始化', gobang.board !== null, '棋盘初始化成功')
      
      // 测试棋盘大小
      this.addTest('棋盘大小', gobang.board.length === 15, `棋盘大小: ${gobang.board.length}x${gobang.board[0].length}`)
      
      // 测试落子
      const moveResult = gobang.makeMove(7, 7, 1)
      this.addTest('落子功能', moveResult, '中心位置落子成功')
      
      // 测试胜负判断
      const hasWin = gobang.checkWin(7, 7, 1)
      this.addTest('胜负判断', !hasWin, '单子不构成胜利')
      
      // 测试AI
      const aiMove = gobang.getAIMove('easy')
      this.addTest('五子棋AI', aiMove !== null, 'AI能够生成走法')
      
      // 测试撤销
      const undoResult = gobang.undoMove()
      this.addTest('撤销功能', undoResult, '撤销走法成功')
      
      console.log('✅ 五子棋引擎测试完成')
    } catch (error) {
      this.addTest('五子棋引擎', false, `错误: ${error.message}`)
      console.error('❌ 五子棋引擎测试失败:', error)
    }
  }
  
  // 添加测试结果
  addTest(name, passed, message) {
    const result = {
      name,
      passed,
      message,
      timestamp: new Date().toISOString()
    }
    
    this.testResults.push(result)
    
    const status = passed ? '✅' : '❌'
    console.log(`${status} ${name}: ${message}`)
  }
  
  // 输出测试结果
  outputResults() {
    const total = this.testResults.length
    const passed = this.testResults.filter(r => r.passed).length
    const failed = total - passed
    
    console.log('\n📊 测试结果汇总:')
    console.log(`总计: ${total} 项`)
    console.log(`通过: ${passed} 项`)
    console.log(`失败: ${failed} 项`)
    console.log(`成功率: ${Math.round((passed / total) * 100)}%`)
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:')
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`- ${r.name}: ${r.message}`))
    }
    
    return {
      total,
      passed,
      failed,
      successRate: Math.round((passed / total) * 100)
    }
  }
  
  // 测试Canvas功能
  testCanvasSupport() {
    console.log('🎨 测试Canvas支持...')
    
    try {
      // 在uni-app中测试Canvas
      const canvasContext = uni.createCanvasContext('test-canvas')
      this.addTest('Canvas支持', canvasContext !== null, 'Canvas上下文创建成功')
      
      // 测试基本绘制功能
      if (canvasContext) {
        canvasContext.fillStyle = '#FF0000'
        canvasContext.fillRect(0, 0, 10, 10)
        this.addTest('Canvas绘制', true, '基本绘制功能正常')
      }
    } catch (error) {
      this.addTest('Canvas支持', false, `Canvas错误: ${error.message}`)
    }
  }
  
  // 测试音频功能
  testAudioSupport() {
    console.log('🔊 测试音频支持...')
    
    try {
      const audio = uni.createInnerAudioContext()
      this.addTest('音频支持', audio !== null, '音频上下文创建成功')
      
      if (audio) {
        audio.destroy()
        this.addTest('音频销毁', true, '音频上下文销毁成功')
      }
    } catch (error) {
      this.addTest('音频支持', false, `音频错误: ${error.message}`)
    }
  }
  
  // 快速诊断
  quickDiagnosis() {
    console.log('🔍 快速诊断游戏问题...')
    
    const issues = []
    
    // 检查引擎
    try {
      new ChessEngine()
    } catch (error) {
      issues.push(`象棋引擎问题: ${error.message}`)
    }
    
    try {
      new GobangEngine()
    } catch (error) {
      issues.push(`五子棋引擎问题: ${error.message}`)
    }
    
    // 检查Canvas
    try {
      uni.createCanvasContext('test')
    } catch (error) {
      issues.push(`Canvas问题: ${error.message}`)
    }
    
    // 检查音频
    try {
      uni.createInnerAudioContext()
    } catch (error) {
      issues.push(`音频问题: ${error.message}`)
    }
    
    if (issues.length === 0) {
      console.log('✅ 快速诊断：未发现明显问题')
      return { status: 'ok', issues: [] }
    } else {
      console.log('❌ 快速诊断：发现以下问题:')
      issues.forEach(issue => console.log(`- ${issue}`))
      return { status: 'error', issues }
    }
  }
}

// 创建全局调试器实例
const gameDebugger = new GameDebugger()

// 在开发环境下自动运行诊断
if (process.env.NODE_ENV === 'development') {
  setTimeout(() => {
    gameDebugger.quickDiagnosis()
  }, 1000)
}

export default gameDebugger
