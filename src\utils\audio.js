/**
 * 音频管理工具类
 * 提供音效池管理、背景音乐播放、音量控制等功能
 */

class AudioManager {
  constructor() {
    // 音效池
    this.soundPool = new Map()
    
    // 背景音乐
    this.backgroundMusic = null
    this.currentBgMusic = null
    
    // 音频设置
    this.settings = {
      soundEnabled: true,
      musicEnabled: true,
      soundVolume: 0.7,
      musicVolume: 0.5
    }
    
    // 音效预设
    this.soundPresets = {
      // 游戏音效
      move: '/static/audio/move.mp3',
      capture: '/static/audio/capture.mp3',
      win: '/static/audio/win.mp3',
      lose: '/static/audio/lose.mp3',
      click: '/static/audio/click.mp3',
      
      // 红包音效
      redpacket_open: '/static/audio/redpacket_open.mp3',
      redpacket_get: '/static/audio/redpacket_get.mp3',
      coin: '/static/audio/coin.mp3',
      
      // 界面音效
      button_click: '/static/audio/button_click.mp3',
      page_turn: '/static/audio/page_turn.mp3',
      success: '/static/audio/success.mp3',
      error: '/static/audio/error.mp3',
      notification: '/static/audio/notification.mp3'
    }
    
    // 背景音乐预设
    this.musicPresets = {
      main: '/static/audio/bg_main.mp3',
      game: '/static/audio/bg_game.mp3',
      relax: '/static/audio/bg_relax.mp3'
    }
    
    // 初始化
    this.init()
  }
  
  /**
   * 初始化音频管理器
   */
  init() {
    // 加载设置
    this.loadSettings()
    
    // 预加载常用音效
    this.preloadSounds(['click', 'button_click', 'success', 'error'])
    
    // 监听应用生命周期
    this.setupLifecycleListeners()
  }
  
  /**
   * 加载音频设置
   */
  loadSettings() {
    try {
      const settings = uni.getStorageSync('audio_settings')
      if (settings) {
        this.settings = { ...this.settings, ...settings }
      }
    } catch (error) {
      console.warn('加载音频设置失败:', error)
    }
  }
  
  /**
   * 保存音频设置
   */
  saveSettings() {
    try {
      uni.setStorageSync('audio_settings', this.settings)
    } catch (error) {
      console.warn('保存音频设置失败:', error)
    }
  }
  
  /**
   * 预加载音效
   */
  preloadSounds(soundNames) {
    soundNames.forEach(name => {
      if (this.soundPresets[name]) {
        this.loadSound(name)
      }
    })
  }
  
  /**
   * 加载音效到音效池
   */
  loadSound(name) {
    if (this.soundPool.has(name)) {
      return this.soundPool.get(name)
    }
    
    const soundPath = this.soundPresets[name]
    if (!soundPath) {
      console.warn(`音效 ${name} 不存在`)
      return null
    }
    
    try {
      const audio = uni.createInnerAudioContext()
      audio.src = soundPath
      audio.volume = this.settings.soundVolume
      
      // 音频事件监听
      audio.onError((error) => {
        console.warn(`音效 ${name} 加载失败:`, error)
        this.soundPool.delete(name)
      })
      
      audio.onEnded(() => {
        audio.seek(0)
      })
      
      this.soundPool.set(name, audio)
      return audio
    } catch (error) {
      console.warn(`创建音效 ${name} 失败:`, error)
      return null
    }
  }
  
  /**
   * 播放音效
   */
  playSound(name, options = {}) {
    if (!this.settings.soundEnabled) return
    
    let audio = this.soundPool.get(name)
    if (!audio) {
      audio = this.loadSound(name)
    }
    
    if (!audio) return
    
    try {
      // 设置音量
      if (options.volume !== undefined) {
        audio.volume = options.volume
      } else {
        audio.volume = this.settings.soundVolume
      }
      
      // 设置循环
      audio.loop = options.loop || false
      
      // 播放
      audio.stop()
      audio.play()
      
      return audio
    } catch (error) {
      console.warn(`播放音效 ${name} 失败:`, error)
    }
  }
  
  /**
   * 停止音效
   */
  stopSound(name) {
    const audio = this.soundPool.get(name)
    if (audio) {
      try {
        audio.stop()
      } catch (error) {
        console.warn(`停止音效 ${name} 失败:`, error)
      }
    }
  }
  
  /**
   * 停止所有音效
   */
  stopAllSounds() {
    this.soundPool.forEach((audio, name) => {
      try {
        audio.stop()
      } catch (error) {
        console.warn(`停止音效 ${name} 失败:`, error)
      }
    })
  }
  
  /**
   * 播放背景音乐
   */
  playBackgroundMusic(name, options = {}) {
    if (!this.settings.musicEnabled) return
    
    // 停止当前背景音乐
    this.stopBackgroundMusic()
    
    const musicPath = this.musicPresets[name]
    if (!musicPath) {
      console.warn(`背景音乐 ${name} 不存在`)
      return
    }
    
    try {
      this.backgroundMusic = uni.createInnerAudioContext()
      this.backgroundMusic.src = musicPath
      this.backgroundMusic.loop = options.loop !== false
      this.backgroundMusic.volume = options.volume || this.settings.musicVolume
      
      this.backgroundMusic.onError((error) => {
        console.warn(`背景音乐 ${name} 播放失败:`, error)
        this.backgroundMusic = null
        this.currentBgMusic = null
      })
      
      this.backgroundMusic.onEnded(() => {
        if (!this.backgroundMusic.loop) {
          this.backgroundMusic = null
          this.currentBgMusic = null
        }
      })
      
      this.backgroundMusic.play()
      this.currentBgMusic = name
      
      return this.backgroundMusic
    } catch (error) {
      console.warn(`创建背景音乐 ${name} 失败:`, error)
    }
  }
  
  /**
   * 停止背景音乐
   */
  stopBackgroundMusic() {
    if (this.backgroundMusic) {
      try {
        this.backgroundMusic.stop()
        this.backgroundMusic.destroy()
      } catch (error) {
        console.warn('停止背景音乐失败:', error)
      }
      this.backgroundMusic = null
      this.currentBgMusic = null
    }
  }
  
  /**
   * 暂停背景音乐
   */
  pauseBackgroundMusic() {
    if (this.backgroundMusic) {
      try {
        this.backgroundMusic.pause()
      } catch (error) {
        console.warn('暂停背景音乐失败:', error)
      }
    }
  }
  
  /**
   * 恢复背景音乐
   */
  resumeBackgroundMusic() {
    if (this.backgroundMusic) {
      try {
        this.backgroundMusic.play()
      } catch (error) {
        console.warn('恢复背景音乐失败:', error)
      }
    }
  }
  
  /**
   * 设置音效开关
   */
  setSoundEnabled(enabled) {
    this.settings.soundEnabled = enabled
    if (!enabled) {
      this.stopAllSounds()
    }
    this.saveSettings()
  }
  
  /**
   * 设置音乐开关
   */
  setMusicEnabled(enabled) {
    this.settings.musicEnabled = enabled
    if (!enabled) {
      this.stopBackgroundMusic()
    }
    this.saveSettings()
  }
  
  /**
   * 设置音效音量
   */
  setSoundVolume(volume) {
    this.settings.soundVolume = Math.max(0, Math.min(1, volume))
    
    // 更新所有音效的音量
    this.soundPool.forEach(audio => {
      audio.volume = this.settings.soundVolume
    })
    
    this.saveSettings()
  }
  
  /**
   * 设置音乐音量
   */
  setMusicVolume(volume) {
    this.settings.musicVolume = Math.max(0, Math.min(1, volume))
    
    if (this.backgroundMusic) {
      this.backgroundMusic.volume = this.settings.musicVolume
    }
    
    this.saveSettings()
  }
  
  /**
   * 设置应用生命周期监听
   */
  setupLifecycleListeners() {
    // 应用进入后台时暂停音频
    uni.onAppHide(() => {
      this.pauseBackgroundMusic()
      this.stopAllSounds()
    })
    
    // 应用回到前台时恢复音频
    uni.onAppShow(() => {
      if (this.settings.musicEnabled && this.currentBgMusic) {
        this.resumeBackgroundMusic()
      }
    })
  }
  
  /**
   * 销毁音频管理器
   */
  destroy() {
    this.stopAllSounds()
    this.stopBackgroundMusic()
    
    // 销毁音效池
    this.soundPool.forEach(audio => {
      try {
        audio.destroy()
      } catch (error) {
        console.warn('销毁音效失败:', error)
      }
    })
    this.soundPool.clear()
  }
  
  /**
   * 获取当前设置
   */
  getSettings() {
    return { ...this.settings }
  }
  
  /**
   * 获取当前播放状态
   */
  getStatus() {
    return {
      soundEnabled: this.settings.soundEnabled,
      musicEnabled: this.settings.musicEnabled,
      currentBgMusic: this.currentBgMusic,
      soundPoolSize: this.soundPool.size
    }
  }
}

// 创建全局音频管理器实例
const audioManager = new AudioManager()

export default audioManager
