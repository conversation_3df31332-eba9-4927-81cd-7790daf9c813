/**
 * 中国象棋游戏引擎
 * 包含棋盘表示、走法生成、AI算法等核心功能
 */

class ChessEngine {
  constructor() {
    // 初始棋盘状态 (10x9)
    this.initialBoard = [
      ['r', 'n', 'b', 'a', 'k', 'a', 'b', 'n', 'r'],
      ['.', '.', '.', '.', '.', '.', '.', '.', '.'],
      ['.', 'c', '.', '.', '.', '.', '.', 'c', '.'],
      ['p', '.', 'p', '.', 'p', '.', 'p', '.', 'p'],
      ['.', '.', '.', '.', '.', '.', '.', '.', '.'],
      ['.', '.', '.', '.', '.', '.', '.', '.', '.'],
      ['P', '.', 'P', '.', 'P', '.', 'P', '.', 'P'],
      ['.', 'C', '.', '.', '.', '.', '.', 'C', '.'],
      ['.', '.', '.', '.', '.', '.', '.', '.', '.'],
      ['R', 'N', 'B', 'A', 'K', 'A', 'B', 'N', 'R']
    ]
    
    // 棋子价值表
    this.pieceValues = {
      'k': 10000, 'K': 10000, // 将/帅
      'r': 500,   'R': 500,   // 车
      'n': 300,   'N': 300,   // 马
      'c': 300,   'C': 300,   // 炮
      'b': 200,   'B': 200,   // 象/相
      'a': 200,   'A': 200,   // 士
      'p': 100,   'P': 100,   // 兵/卒
      '.': 0
    }
    
    // 位置价值表
    this.positionValues = this.initPositionValues()
    
    this.reset()
  }
  
  // 重置游戏
  reset() {
    this.board = this.initialBoard.map(row => [...row])
    this.currentPlayer = 'red' // red: 红方(大写), black: 黑方(小写)
    this.moveHistory = []
    this.gameOver = false
    this.winner = null
  }
  
  // 初始化位置价值表
  initPositionValues() {
    const values = {}
    
    // 兵/卒位置价值
    values.p = [
      [0, 0, 0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0, 0, 0],
      [10, 20, 30, 40, 50, 40, 30, 20, 10],
      [20, 30, 40, 50, 60, 50, 40, 30, 20],
      [30, 40, 50, 60, 70, 60, 50, 40, 30],
      [40, 50, 60, 70, 80, 70, 60, 50, 40],
      [50, 60, 70, 80, 90, 80, 70, 60, 50]
    ]
    
    values.P = values.p.slice().reverse()
    
    // 车位置价值
    values.r = values.R = [
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180],
      [180, 180, 180, 180, 180, 180, 180, 180, 180]
    ]
    
    // 马位置价值
    values.n = values.N = [
      [90, 90, 90, 96, 90, 96, 90, 90, 90],
      [90, 96, 103, 97, 94, 97, 103, 96, 90],
      [92, 98, 99, 103, 99, 103, 99, 98, 92],
      [93, 108, 100, 107, 100, 107, 100, 108, 93],
      [90, 100, 99, 103, 104, 103, 99, 100, 90],
      [90, 98, 101, 102, 103, 102, 101, 98, 90],
      [92, 94, 98, 95, 98, 95, 98, 94, 92],
      [93, 92, 94, 95, 92, 95, 94, 92, 93],
      [85, 90, 92, 93, 78, 93, 92, 90, 85],
      [88, 85, 90, 88, 90, 88, 90, 85, 88]
    ]
    
    return values
  }
  
  // 检查坐标是否在棋盘内
  isValidPosition(row, col) {
    return row >= 0 && row < 10 && col >= 0 && col < 9
  }
  
  // 检查是否为己方棋子
  isOwnPiece(piece, player) {
    if (piece === '.') return false
    if (player === 'red') return piece >= 'A' && piece <= 'Z'
    return piece >= 'a' && piece <= 'z'
  }
  
  // 检查是否为敌方棋子
  isEnemyPiece(piece, player) {
    if (piece === '.') return false
    if (player === 'red') return piece >= 'a' && piece <= 'z'
    return piece >= 'A' && piece <= 'Z'
  }
  
  // 生成所有可能的走法
  generateMoves(player = this.currentPlayer) {
    const moves = []
    
    for (let row = 0; row < 10; row++) {
      for (let col = 0; col < 9; col++) {
        const piece = this.board[row][col]
        if (this.isOwnPiece(piece, player)) {
          const pieceMoves = this.generatePieceMoves(row, col, piece.toLowerCase())
          moves.push(...pieceMoves)
        }
      }
    }
    
    return moves
  }
  
  // 生成单个棋子的走法
  generatePieceMoves(row, col, pieceType) {
    const moves = []
    const player = this.isOwnPiece(this.board[row][col], 'red') ? 'red' : 'black'
    
    switch (pieceType) {
      case 'k': // 将/帅
        moves.push(...this.generateKingMoves(row, col, player))
        break
      case 'r': // 车
        moves.push(...this.generateRookMoves(row, col, player))
        break
      case 'n': // 马
        moves.push(...this.generateKnightMoves(row, col, player))
        break
      case 'c': // 炮
        moves.push(...this.generateCannonMoves(row, col, player))
        break
      case 'b': // 象/相
        moves.push(...this.generateBishopMoves(row, col, player))
        break
      case 'a': // 士
        moves.push(...this.generateAdvisorMoves(row, col, player))
        break
      case 'p': // 兵/卒
        moves.push(...this.generatePawnMoves(row, col, player))
        break
    }
    
    return moves
  }
  
  // 将/帅走法
  generateKingMoves(row, col, player) {
    const moves = []
    const directions = [[0, 1], [0, -1], [1, 0], [-1, 0]]
    
    // 确定宫的范围
    const palaceRows = player === 'red' ? [7, 8, 9] : [0, 1, 2]
    const palaceCols = [3, 4, 5]
    
    for (const [dr, dc] of directions) {
      const newRow = row + dr
      const newCol = col + dc
      
      if (palaceRows.includes(newRow) && palaceCols.includes(newCol)) {
        const targetPiece = this.board[newRow][newCol]
        if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
          moves.push({ from: [row, col], to: [newRow, newCol] })
        }
      }
    }
    
    return moves
  }
  
  // 车走法
  generateRookMoves(row, col, player) {
    const moves = []
    const directions = [[0, 1], [0, -1], [1, 0], [-1, 0]]
    
    for (const [dr, dc] of directions) {
      for (let i = 1; i < 10; i++) {
        const newRow = row + dr * i
        const newCol = col + dc * i
        
        if (!this.isValidPosition(newRow, newCol)) break
        
        const targetPiece = this.board[newRow][newCol]
        if (targetPiece === '.') {
          moves.push({ from: [row, col], to: [newRow, newCol] })
        } else {
          if (this.isEnemyPiece(targetPiece, player)) {
            moves.push({ from: [row, col], to: [newRow, newCol] })
          }
          break
        }
      }
    }
    
    return moves
  }
  
  // 马走法
  generateKnightMoves(row, col, player) {
    const moves = []
    const knightMoves = [
      [-2, -1], [-2, 1], [-1, -2], [-1, 2],
      [1, -2], [1, 2], [2, -1], [2, 1]
    ]
    
    // 马腿位置
    const legPositions = [
      [-1, 0], [-1, 0], [0, -1], [0, 1],
      [0, -1], [0, 1], [1, 0], [1, 0]
    ]
    
    for (let i = 0; i < knightMoves.length; i++) {
      const [dr, dc] = knightMoves[i]
      const [legDr, legDc] = legPositions[i]
      
      const newRow = row + dr
      const newCol = col + dc
      const legRow = row + legDr
      const legCol = col + legDc
      
      // 检查马腿是否被堵
      if (this.isValidPosition(legRow, legCol) && this.board[legRow][legCol] !== '.') {
        continue
      }
      
      if (this.isValidPosition(newRow, newCol)) {
        const targetPiece = this.board[newRow][newCol]
        if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
          moves.push({ from: [row, col], to: [newRow, newCol] })
        }
      }
    }
    
    return moves
  }
  
  // 炮走法
  generateCannonMoves(row, col, player) {
    const moves = []
    const directions = [[0, 1], [0, -1], [1, 0], [-1, 0]]
    
    for (const [dr, dc] of directions) {
      let hasJumped = false
      
      for (let i = 1; i < 10; i++) {
        const newRow = row + dr * i
        const newCol = col + dc * i
        
        if (!this.isValidPosition(newRow, newCol)) break
        
        const targetPiece = this.board[newRow][newCol]
        
        if (!hasJumped) {
          if (targetPiece === '.') {
            moves.push({ from: [row, col], to: [newRow, newCol] })
          } else {
            hasJumped = true
          }
        } else {
          if (targetPiece !== '.') {
            if (this.isEnemyPiece(targetPiece, player)) {
              moves.push({ from: [row, col], to: [newRow, newCol] })
            }
            break
          }
        }
      }
    }
    
    return moves
  }
  
  // 象/相走法
  generateBishopMoves(row, col, player) {
    const moves = []
    const bishopMoves = [[-2, -2], [-2, 2], [2, -2], [2, 2]]
    const eyePositions = [[-1, -1], [-1, 1], [1, -1], [1, 1]]
    
    // 确定象的活动范围
    const validRows = player === 'red' ? [5, 6, 7, 8, 9] : [0, 1, 2, 3, 4]
    
    for (let i = 0; i < bishopMoves.length; i++) {
      const [dr, dc] = bishopMoves[i]
      const [eyeDr, eyeDc] = eyePositions[i]
      
      const newRow = row + dr
      const newCol = col + dc
      const eyeRow = row + eyeDr
      const eyeCol = col + eyeDc
      
      // 检查象眼是否被堵
      if (this.isValidPosition(eyeRow, eyeCol) && this.board[eyeRow][eyeCol] !== '.') {
        continue
      }
      
      if (this.isValidPosition(newRow, newCol) && validRows.includes(newRow)) {
        const targetPiece = this.board[newRow][newCol]
        if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
          moves.push({ from: [row, col], to: [newRow, newCol] })
        }
      }
    }
    
    return moves
  }
  
  // 士走法
  generateAdvisorMoves(row, col, player) {
    const moves = []
    const advisorMoves = [[-1, -1], [-1, 1], [1, -1], [1, 1]]
    
    // 确定宫的范围
    const palaceRows = player === 'red' ? [7, 8, 9] : [0, 1, 2]
    const palaceCols = [3, 4, 5]
    
    for (const [dr, dc] of advisorMoves) {
      const newRow = row + dr
      const newCol = col + dc
      
      if (palaceRows.includes(newRow) && palaceCols.includes(newCol)) {
        const targetPiece = this.board[newRow][newCol]
        if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
          moves.push({ from: [row, col], to: [newRow, newCol] })
        }
      }
    }
    
    return moves
  }
  
  // 兵/卒走法
  generatePawnMoves(row, col, player) {
    const moves = []
    
    if (player === 'red') {
      // 红兵向上走
      if (row > 0) {
        const targetPiece = this.board[row - 1][col]
        if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
          moves.push({ from: [row, col], to: [row - 1, col] })
        }
      }
      
      // 过河后可以横走
      if (row <= 4) {
        for (const dc of [-1, 1]) {
          const newCol = col + dc
          if (this.isValidPosition(row, newCol)) {
            const targetPiece = this.board[row][newCol]
            if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
              moves.push({ from: [row, col], to: [row, newCol] })
            }
          }
        }
      }
    } else {
      // 黑卒向下走
      if (row < 9) {
        const targetPiece = this.board[row + 1][col]
        if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
          moves.push({ from: [row, col], to: [row + 1, col] })
        }
      }
      
      // 过河后可以横走
      if (row >= 5) {
        for (const dc of [-1, 1]) {
          const newCol = col + dc
          if (this.isValidPosition(row, newCol)) {
            const targetPiece = this.board[row][newCol]
            if (targetPiece === '.' || this.isEnemyPiece(targetPiece, player)) {
              moves.push({ from: [row, col], to: [row, newCol] })
            }
          }
        }
      }
    }
    
    return moves
  }
  
  // 执行走法
  makeMove(move) {
    const { from, to } = move
    const [fromRow, fromCol] = from
    const [toRow, toCol] = to
    
    const piece = this.board[fromRow][fromCol]
    const capturedPiece = this.board[toRow][toCol]
    
    // 记录走法
    this.moveHistory.push({
      move,
      piece,
      capturedPiece,
      player: this.currentPlayer
    })
    
    // 执行移动
    this.board[toRow][toCol] = piece
    this.board[fromRow][fromCol] = '.'
    
    // 检查游戏结束
    this.checkGameOver()
    
    // 切换玩家
    this.currentPlayer = this.currentPlayer === 'red' ? 'black' : 'red'
    
    return true
  }
  
  // 撤销走法
  undoMove() {
    if (this.moveHistory.length === 0) return false
    
    const lastMove = this.moveHistory.pop()
    const { move, piece, capturedPiece } = lastMove
    const { from, to } = move
    const [fromRow, fromCol] = from
    const [toRow, toCol] = to
    
    // 恢复棋盘
    this.board[fromRow][fromCol] = piece
    this.board[toRow][toCol] = capturedPiece
    
    // 切换玩家
    this.currentPlayer = this.currentPlayer === 'red' ? 'black' : 'red'
    
    // 重置游戏状态
    this.gameOver = false
    this.winner = null
    
    return true
  }
  
  // 检查游戏是否结束
  checkGameOver() {
    // 检查将帅是否被吃
    let redKingExists = false
    let blackKingExists = false
    
    for (let row = 0; row < 10; row++) {
      for (let col = 0; col < 9; col++) {
        const piece = this.board[row][col]
        if (piece === 'K') redKingExists = true
        if (piece === 'k') blackKingExists = true
      }
    }
    
    if (!redKingExists) {
      this.gameOver = true
      this.winner = 'black'
      return true
    }
    
    if (!blackKingExists) {
      this.gameOver = true
      this.winner = 'red'
      return true
    }
    
    // 检查是否无子可动
    const moves = this.generateMoves(this.currentPlayer)
    if (moves.length === 0) {
      this.gameOver = true
      this.winner = this.currentPlayer === 'red' ? 'black' : 'red'
      return true
    }
    
    return false
  }
  
  // 评估棋盘局面
  evaluate() {
    let score = 0
    
    for (let row = 0; row < 10; row++) {
      for (let col = 0; col < 9; col++) {
        const piece = this.board[row][col]
        if (piece === '.') continue
        
        const pieceValue = this.pieceValues[piece]
        const positionValue = this.getPositionValue(piece, row, col)
        const totalValue = pieceValue + positionValue
        
        if (piece >= 'A' && piece <= 'Z') {
          score += totalValue // 红方
        } else {
          score -= totalValue // 黑方
        }
      }
    }
    
    return score
  }
  
  // 获取位置价值
  getPositionValue(piece, row, col) {
    const pieceType = piece.toLowerCase()
    if (this.positionValues[pieceType]) {
      return this.positionValues[pieceType][row][col] || 0
    }
    return 0
  }
  
  // 复制棋盘
  copyBoard() {
    return this.board.map(row => [...row])
  }
  
  // 获取棋盘状态
  getBoardState() {
    return {
      board: this.copyBoard(),
      currentPlayer: this.currentPlayer,
      gameOver: this.gameOver,
      winner: this.winner,
      moveCount: this.moveHistory.length
    }
  }

  // AI算法 - Minimax with Alpha-Beta剪枝
  getBestMove(depth = 3, isMaximizing = false, alpha = -Infinity, beta = Infinity) {
    if (depth === 0 || this.gameOver) {
      return { score: this.evaluate(), move: null }
    }

    const moves = this.generateMoves(this.currentPlayer)
    if (moves.length === 0) {
      return { score: isMaximizing ? -Infinity : Infinity, move: null }
    }

    let bestMove = null
    let bestScore = isMaximizing ? -Infinity : Infinity

    // 移动排序优化
    const sortedMoves = this.sortMoves(moves)

    for (const move of sortedMoves) {
      // 执行走法
      const originalBoard = this.copyBoard()
      const originalPlayer = this.currentPlayer
      const originalGameOver = this.gameOver
      const originalWinner = this.winner

      this.makeMove(move)

      // 递归搜索
      const result = this.getBestMove(depth - 1, !isMaximizing, alpha, beta)
      const score = result.score

      // 恢复棋盘状态
      this.board = originalBoard
      this.currentPlayer = originalPlayer
      this.gameOver = originalGameOver
      this.winner = originalWinner
      this.moveHistory.pop()

      // 更新最佳走法
      if (isMaximizing) {
        if (score > bestScore) {
          bestScore = score
          bestMove = move
        }
        alpha = Math.max(alpha, score)
      } else {
        if (score < bestScore) {
          bestScore = score
          bestMove = move
        }
        beta = Math.min(beta, score)
      }

      // Alpha-Beta剪枝
      if (beta <= alpha) {
        break
      }
    }

    return { score: bestScore, move: bestMove }
  }

  // 移动排序优化
  sortMoves(moves) {
    return moves.sort((a, b) => {
      const scoreA = this.evaluateMove(a)
      const scoreB = this.evaluateMove(b)
      return scoreB - scoreA
    })
  }

  // 评估单个走法
  evaluateMove(move) {
    const { from, to } = move
    const [fromRow, fromCol] = from
    const [toRow, toCol] = to

    let score = 0

    // 吃子价值
    const capturedPiece = this.board[toRow][toCol]
    if (capturedPiece !== '.') {
      score += this.pieceValues[capturedPiece]
    }

    // 位置价值变化
    const piece = this.board[fromRow][fromCol]
    const fromPositionValue = this.getPositionValue(piece, fromRow, fromCol)
    const toPositionValue = this.getPositionValue(piece, toRow, toCol)
    score += toPositionValue - fromPositionValue

    return score
  }

  // 获取AI走法（简化版本，用于快速响应）
  getAIMove(difficulty = 'medium') {
    const depths = {
      easy: 2,
      medium: 3,
      hard: 4
    }

    const depth = depths[difficulty] || 3
    const result = this.getBestMove(depth, this.currentPlayer === 'black')

    return result.move
  }

  // 检查走法是否合法
  isValidMove(move) {
    const { from, to } = move
    const [fromRow, fromCol] = from
    const [toRow, toCol] = to

    // 检查坐标是否有效
    if (!this.isValidPosition(fromRow, fromCol) || !this.isValidPosition(toRow, toCol)) {
      return false
    }

    // 检查起始位置是否有己方棋子
    const piece = this.board[fromRow][fromCol]
    if (!this.isOwnPiece(piece, this.currentPlayer)) {
      return false
    }

    // 检查目标位置是否为己方棋子
    const targetPiece = this.board[toRow][toCol]
    if (this.isOwnPiece(targetPiece, this.currentPlayer)) {
      return false
    }

    // 生成该棋子的所有合法走法
    const validMoves = this.generatePieceMoves(fromRow, fromCol, piece.toLowerCase())

    // 检查目标走法是否在合法走法中
    return validMoves.some(validMove =>
      validMove.to[0] === toRow && validMove.to[1] === toCol
    )
  }

  // 获取棋子名称
  getPieceName(piece) {
    const names = {
      'k': '将', 'K': '帅',
      'r': '车', 'R': '车',
      'n': '马', 'N': '马',
      'c': '炮', 'C': '炮',
      'b': '象', 'B': '相',
      'a': '士', 'A': '士',
      'p': '卒', 'P': '兵'
    }
    return names[piece] || ''
  }

  // 获取位置描述
  getPositionName(row, col) {
    const cols = ['一', '二', '三', '四', '五', '六', '七', '八', '九']
    const rows = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
    return cols[col] + rows[9 - row]
  }

  // 获取走法描述
  getMoveDescription(move) {
    const { from, to } = move
    const [fromRow, fromCol] = from
    const [toRow, toCol] = to

    const piece = this.board[fromRow][fromCol]
    const pieceName = this.getPieceName(piece)
    const fromPos = this.getPositionName(fromRow, fromCol)
    const toPos = this.getPositionName(toRow, toCol)

    return `${pieceName}${fromPos}到${toPos}`
  }
}

export default ChessEngine
