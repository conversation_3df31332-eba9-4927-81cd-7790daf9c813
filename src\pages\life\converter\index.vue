<template>
  <view class="converter-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content">
        <text class="page-title">单位转换器</text>
        <text class="page-desc">快速进行各种单位转换</text>
      </view>
    </view>
    
    <!-- 转换类型选择 -->
    <view class="type-section">
      <view class="type-tabs">
        <button 
          v-for="type in conversionTypes" 
          :key="type.key"
          class="type-tab"
          :class="{ active: selectedType === type.key }"
          @tap="selectType(type.key)"
        >
          <text class="tab-icon">{{ type.icon }}</text>
          <text class="tab-text">{{ type.name }}</text>
        </button>
      </view>
    </view>
    
    <!-- 转换器主体 -->
    <view class="converter-section">
      <view class="converter-card card">
        <!-- 输入区域 -->
        <view class="input-section">
          <view class="input-group">
            <text class="input-label">从</text>
            <view class="input-row">
              <input 
                class="value-input" 
                type="digit"
                placeholder="请输入数值"
                v-model="inputValue"
                @input="onInputChange"
              />
              <picker 
                :value="fromUnitIndex" 
                :range="unitOptions" 
                range-key="name"
                @change="onFromUnitChange"
              >
                <view class="unit-picker">
                  <text class="unit-text">{{ selectedFromUnit?.name || '选择单位' }}</text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
            </view>
          </view>
          
          <!-- 交换按钮 -->
          <view class="swap-section">
            <button class="swap-btn" @tap="swapUnits">
              <text class="swap-icon">⇅</text>
            </button>
          </view>
          
          <!-- 输出区域 -->
          <view class="output-group">
            <text class="output-label">到</text>
            <view class="output-row">
              <view class="result-display">
                <text class="result-text">{{ result || '0' }}</text>
              </view>
              <picker 
                :value="toUnitIndex" 
                :range="unitOptions" 
                range-key="name"
                @change="onToUnitChange"
              >
                <view class="unit-picker">
                  <text class="unit-text">{{ selectedToUnit?.name || '选择单位' }}</text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
            </view>
          </view>
        </view>
        
        <!-- 快速转换按钮 -->
        <view class="quick-actions">
          <button class="quick-btn" @tap="clearInput">清空</button>
          <button class="quick-btn" @tap="copyResult">复制结果</button>
          <button class="quick-btn" @tap="showBatchConvert">批量转换</button>
        </view>
      </view>
    </view>
    
    <!-- 常用转换 -->
    <view class="common-section">
      <view class="section-title">
        <text class="title-text">常用转换</text>
        <text class="title-desc">快速访问常用单位转换</text>
      </view>
      
      <view class="common-list">
        <view 
          v-for="common in commonConversions" 
          :key="common.name"
          class="common-item card"
          @tap="useCommonConversion(common)"
        >
          <text class="common-name">{{ common.name }}</text>
          <text class="common-arrow">></text>
        </view>
      </view>
    </view>
    
    <!-- 转换历史 -->
    <view class="history-section" v-if="conversionHistory.length > 0">
      <view class="section-title">
        <text class="title-text">转换历史</text>
        <button class="clear-history-btn" @tap="clearHistory">清空</button>
      </view>
      
      <view class="history-list">
        <view 
          v-for="(item, index) in conversionHistory" 
          :key="index"
          class="history-item card"
          @tap="useHistoryItem(item)"
        >
          <view class="history-content">
            <text class="history-conversion">{{ item.value }} {{ item.fromUnit }} = {{ item.result }} {{ item.toUnit }}</text>
            <text class="history-time">{{ formatTime(item.timestamp) }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 批量转换弹窗 -->
    <view v-if="showBatchModal" class="batch-modal" @tap="closeBatchModal">
      <view class="modal-content" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">批量转换</text>
          <button class="close-btn" @tap="closeBatchModal">×</button>
        </view>
        
        <view class="batch-input">
          <text class="batch-label">{{ inputValue }} {{ selectedFromUnit?.name }}</text>
        </view>
        
        <scroll-view class="batch-results" scroll-y>
          <view 
            v-for="item in batchResults" 
            :key="item.unit"
            class="batch-item"
          >
            <text class="batch-unit">{{ item.unitName || item.unit }}</text>
            <text class="batch-value">{{ item.value }}</text>
          </view>
        </scroll-view>
        
        <button class="copy-all-btn btn-primary" @tap="copyAllResults">
          <text class="btn-text">复制全部</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRedPacketStore } from '../../../stores/redpacket.js'
import unitConverter from '../../../utils/unit-converter.js'
import audioManager from '../../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 转换状态
const selectedType = ref('length')
const inputValue = ref('')
const result = ref('')
const fromUnitIndex = ref(0)
const toUnitIndex = ref(1)
const showBatchModal = ref(false)
const batchResults = ref([])
const conversionHistory = ref([])

// 转换类型
const conversionTypes = ref([])
const unitOptions = ref([])
const commonConversions = ref([])

// 计算属性
const selectedFromUnit = computed(() => unitOptions.value[fromUnitIndex.value])
const selectedToUnit = computed(() => unitOptions.value[toUnitIndex.value])

// 页面加载
onMounted(() => {
  initConverter()
  loadHistory()
  audioManager.playSound('page_turn')
})

// 监听输入变化
watch([inputValue, selectedFromUnit, selectedToUnit], () => {
  performConversion()
})

// 初始化转换器
const initConverter = () => {
  conversionTypes.value = unitConverter.getConversionTypes()
  commonConversions.value = unitConverter.getCommonConversions()
  selectType('length')
}

// 选择转换类型
const selectType = (type) => {
  selectedType.value = type
  unitOptions.value = unitConverter.getUnits(type)
  
  // 重置选择
  fromUnitIndex.value = 0
  toUnitIndex.value = Math.min(1, unitOptions.value.length - 1)
  
  // 重新转换
  performConversion()
  
  audioManager.playSound('click')
}

// 输入变化处理
const onInputChange = (e) => {
  inputValue.value = e.detail.value
}

// 源单位变化
const onFromUnitChange = (e) => {
  fromUnitIndex.value = e.detail.value
  audioManager.playSound('click')
}

// 目标单位变化
const onToUnitChange = (e) => {
  toUnitIndex.value = e.detail.value
  audioManager.playSound('click')
}

// 执行转换
const performConversion = () => {
  if (!inputValue.value || !selectedFromUnit.value || !selectedToUnit.value) {
    result.value = ''
    return
  }
  
  const validation = unitConverter.validateInput(inputValue.value, selectedType.value)
  if (!validation.valid) {
    result.value = validation.error
    return
  }
  
  const conversionResult = unitConverter.convert(
    inputValue.value,
    selectedFromUnit.value.key,
    selectedToUnit.value.key,
    selectedType.value
  )
  
  if (conversionResult.success) {
    result.value = conversionResult.result
    
    // 触发工具使用统计
    redPacketStore.onToolUsed()
  } else {
    result.value = conversionResult.error
  }
}

// 交换单位
const swapUnits = () => {
  const tempIndex = fromUnitIndex.value
  fromUnitIndex.value = toUnitIndex.value
  toUnitIndex.value = tempIndex
  
  // 如果有结果，将结果作为新的输入值
  if (result.value && !isNaN(parseFloat(result.value))) {
    inputValue.value = result.value
  }
  
  audioManager.playSound('click')
}

// 清空输入
const clearInput = () => {
  inputValue.value = ''
  result.value = ''
  audioManager.playSound('click')
}

// 复制结果
const copyResult = () => {
  if (!result.value) {
    uni.showToast({
      title: '没有可复制的结果',
      icon: 'none'
    })
    return
  }
  
  uni.setClipboardData({
    data: result.value,
    success: () => {
      audioManager.playSound('success')
      uni.showToast({
        title: '已复制到剪贴板',
        icon: 'success'
      })
    }
  })
}

// 显示批量转换
const showBatchConvert = () => {
  if (!inputValue.value || !selectedFromUnit.value) {
    uni.showToast({
      title: '请先输入数值和选择单位',
      icon: 'none'
    })
    return
  }
  
  try {
    batchResults.value = unitConverter.convertBatch(
      inputValue.value,
      selectedFromUnit.value.key,
      selectedType.value
    )
    showBatchModal.value = true
    audioManager.playSound('click')
  } catch (error) {
    uni.showToast({
      title: '批量转换失败',
      icon: 'error'
    })
  }
}

// 关闭批量转换弹窗
const closeBatchModal = () => {
  showBatchModal.value = false
}

// 复制所有结果
const copyAllResults = () => {
  const allResults = batchResults.value.map(item => 
    `${item.unitName || item.unit}: ${item.value}`
  ).join('\n')
  
  uni.setClipboardData({
    data: allResults,
    success: () => {
      audioManager.playSound('success')
      uni.showToast({
        title: '已复制全部结果',
        icon: 'success'
      })
      closeBatchModal()
    }
  })
}

// 使用常用转换
const useCommonConversion = (common) => {
  selectType(common.type)
  
  // 等待类型切换完成
  setTimeout(() => {
    const fromIndex = unitOptions.value.findIndex(u => u.key === common.from)
    const toIndex = unitOptions.value.findIndex(u => u.key === common.to)
    
    if (fromIndex >= 0) fromUnitIndex.value = fromIndex
    if (toIndex >= 0) toUnitIndex.value = toIndex
    
    audioManager.playSound('click')
  }, 100)
}

// 使用历史记录项
const useHistoryItem = (item) => {
  selectType(item.type)
  
  setTimeout(() => {
    const fromIndex = unitOptions.value.findIndex(u => u.key === item.fromUnit)
    const toIndex = unitOptions.value.findIndex(u => u.key === item.toUnit)
    
    if (fromIndex >= 0) fromUnitIndex.value = fromIndex
    if (toIndex >= 0) toUnitIndex.value = toIndex
    
    inputValue.value = item.value.toString()
    
    audioManager.playSound('click')
  }, 100)
}

// 加载历史记录
const loadHistory = () => {
  try {
    const history = uni.getStorageSync('converter_history')
    if (history && Array.isArray(history)) {
      conversionHistory.value = history.slice(0, 10) // 只显示最近10条
      unitConverter.history = history
    }
  } catch (error) {
    console.warn('加载转换历史失败:', error)
  }
}

// 保存历史记录
const saveHistory = () => {
  try {
    uni.setStorageSync('converter_history', unitConverter.getHistory())
  } catch (error) {
    console.warn('保存转换历史失败:', error)
  }
}

// 清空历史记录
const clearHistory = () => {
  unitConverter.clearHistory()
  conversionHistory.value = []
  saveHistory()
  audioManager.playSound('click')
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 86400000) { // 24小时内
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  } else {
    return date.toLocaleDateString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit' 
    })
  }
}

// 监听历史记录变化
watch(() => unitConverter.getHistory(), (newHistory) => {
  conversionHistory.value = newHistory.slice(0, 10)
  saveHistory()
}, { deep: true })
</script>

<style lang="scss" scoped>
.converter-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

.header {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.header-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-sm;
}

.page-desc {
  font-size: $font-size-md;
  opacity: 0.9;
}

.type-section {
  padding: $spacing-md;
  margin-top: -40rpx;
  position: relative;
  z-index: 1;
}

.type-tabs {
  display: flex;
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-sm;
  box-shadow: $shadow-lg;
  overflow-x: auto;
  gap: $spacing-sm;
}

.type-tab {
  flex-shrink: 0;
  min-width: 120rpx;
  padding: $spacing-sm;
  border-radius: $border-radius-md;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
  
  &.active {
    background: $primary-color;
    color: white;
  }
}

.tab-icon {
  font-size: $font-size-lg;
}

.tab-text {
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
}

.converter-section, .common-section, .history-section {
  padding: $spacing-md;
}

.converter-card {
  padding: $spacing-lg;
}

.input-section {
  margin-bottom: $spacing-lg;
}

.input-group, .output-group {
  margin-bottom: $spacing-md;
}

.input-label, .output-label {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
}

.input-row, .output-row {
  display: flex;
  gap: $spacing-sm;
}

.value-input {
  flex: 1;
  height: 80rpx;
  padding: 0 $spacing-md;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-md;
  font-size: $font-size-lg;
  background: white;
}

.result-display {
  flex: 1;
  height: 80rpx;
  padding: 0 $spacing-md;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-md;
  background: $bg-color-light;
  display: flex;
  align-items: center;
}

.result-text {
  font-size: $font-size-lg;
  color: $text-color-primary;
  font-weight: $font-weight-medium;
  font-family: 'Courier New', monospace;
}

.unit-picker {
  min-width: 200rpx;
  height: 80rpx;
  padding: 0 $spacing-md;
  border: 2rpx solid $border-color-light;
  border-radius: $border-radius-md;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.unit-text {
  font-size: $font-size-md;
  color: $text-color-primary;
}

.picker-arrow {
  font-size: $font-size-sm;
  color: $text-color-light;
}

.swap-section {
  display: flex;
  justify-content: center;
  margin: $spacing-md 0;
}

.swap-btn {
  width: 80rpx;
  height: 80rpx;
  background: $primary-color;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swap-icon {
  font-size: $font-size-xl;
  color: white;
  font-weight: $font-weight-bold;
}

.quick-actions {
  display: flex;
  gap: $spacing-sm;
}

.quick-btn {
  flex: 1;
  height: 60rpx;
  background: $bg-color-light;
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.title-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.clear-history-btn {
  padding: $spacing-sm $spacing-md;
  background: $danger-color;
  color: white;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
}

.common-list, .history-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.common-item, .history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
}

.common-name {
  font-size: $font-size-md;
  color: $text-color-primary;
}

.common-arrow {
  font-size: $font-size-lg;
  color: $text-color-light;
}

.history-content {
  flex: 1;
}

.history-conversion {
  font-size: $font-size-md;
  color: $text-color-primary;
  font-family: 'Courier New', monospace;
  display: block;
  margin-bottom: 5rpx;
}

.history-time {
  font-size: $font-size-sm;
  color: $text-color-light;
  display: block;
}

.batch-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
}

.modal-content {
  background: white;
  border-radius: $border-radius-lg;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-lg;
  border-bottom: 1rpx solid $border-color-light;
}

.modal-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background: $bg-color-light;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-xl;
  color: $text-color-secondary;
}

.batch-input {
  padding: $spacing-lg;
  text-align: center;
  border-bottom: 1rpx solid $border-color-light;
}

.batch-label {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.batch-results {
  flex: 1;
  padding: $spacing-md;
}

.batch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
  border-bottom: 1rpx solid $border-color-light;
  
  &:last-child {
    border-bottom: none;
  }
}

.batch-unit {
  font-size: $font-size-md;
  color: $text-color-primary;
}

.batch-value {
  font-size: $font-size-md;
  color: $primary-color;
  font-weight: $font-weight-medium;
  font-family: 'Courier New', monospace;
}

.copy-all-btn {
  margin: $spacing-md;
  height: 80rpx;
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
}
</style>
