/**
 * 科学计算器工具类
 * 支持基础运算、科学函数、表达式解析等功能
 */

class Calculator {
  constructor() {
    this.history = []
    this.memory = 0
    this.angleMode = 'deg' // 'deg' 或 'rad'
    
    // 运算符优先级
    this.precedence = {
      '+': 1,
      '-': 1,
      '*': 2,
      '/': 2,
      '%': 2,
      '^': 3,
      '**': 3
    }
    
    // 科学函数映射
    this.functions = {
      'sin': Math.sin,
      'cos': Math.cos,
      'tan': Math.tan,
      'asin': Math.asin,
      'acos': Math.acos,
      'atan': Math.atan,
      'sinh': Math.sinh,
      'cosh': Math.cosh,
      'tanh': Math.tanh,
      'log': Math.log10,
      'ln': Math.log,
      'sqrt': Math.sqrt,
      'abs': Math.abs,
      'ceil': Math.ceil,
      'floor': Math.floor,
      'round': Math.round,
      'exp': Math.exp
    }
    
    // 常数
    this.constants = {
      'π': Math.PI,
      'e': Math.E,
      'pi': Math.PI
    }
  }
  
  /**
   * 角度转弧度
   */
  toRadians(degrees) {
    return degrees * Math.PI / 180
  }
  
  /**
   * 弧度转角度
   */
  toDegrees(radians) {
    return radians * 180 / Math.PI
  }
  
  /**
   * 处理三角函数的角度模式
   */
  handleAngleMode(value, isInverse = false) {
    if (this.angleMode === 'deg') {
      return isInverse ? this.toDegrees(value) : this.toRadians(value)
    }
    return value
  }
  
  /**
   * 计算表达式
   */
  calculate(expression) {
    try {
      // 预处理表达式
      const processedExpression = this.preprocessExpression(expression)
      
      // 解析并计算
      const result = this.evaluateExpression(processedExpression)
      
      // 检查结果有效性
      if (!isFinite(result)) {
        throw new Error('计算结果无效')
      }
      
      // 添加到历史记录
      this.addToHistory(expression, result)
      
      return {
        success: true,
        result: this.formatResult(result),
        rawResult: result
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || '计算错误'
      }
    }
  }
  
  /**
   * 预处理表达式
   */
  preprocessExpression(expression) {
    let processed = expression.toString().trim()
    
    // 替换常数
    Object.keys(this.constants).forEach(constant => {
      const regex = new RegExp(`\\b${constant}\\b`, 'g')
      processed = processed.replace(regex, this.constants[constant])
    })
    
    // 处理隐式乘法 (如 2π -> 2*π)
    processed = processed.replace(/(\d)([a-zA-Z])/g, '$1*$2')
    processed = processed.replace(/([a-zA-Z])(\d)/g, '$1*$2')
    processed = processed.replace(/(\))(\d)/g, '$1*$2')
    processed = processed.replace(/(\d)(\()/g, '$1*$2')
    
    // 处理百分号
    processed = processed.replace(/(\d+(?:\.\d+)?)%/g, '($1/100)')
    
    // 处理阶乘
    processed = this.handleFactorial(processed)
    
    return processed
  }
  
  /**
   * 处理阶乘
   */
  handleFactorial(expression) {
    return expression.replace(/(\d+(?:\.\d+)?)!/g, (match, num) => {
      const n = parseInt(num)
      if (n < 0 || n > 170) throw new Error('阶乘参数超出范围')
      return this.factorial(n).toString()
    })
  }
  
  /**
   * 计算阶乘
   */
  factorial(n) {
    if (n === 0 || n === 1) return 1
    let result = 1
    for (let i = 2; i <= n; i++) {
      result *= i
    }
    return result
  }
  
  /**
   * 表达式求值（使用调度场算法）
   */
  evaluateExpression(expression) {
    const tokens = this.tokenize(expression)
    const outputQueue = []
    const operatorStack = []
    
    for (const token of tokens) {
      if (this.isNumber(token)) {
        outputQueue.push(parseFloat(token))
      } else if (this.isFunction(token)) {
        operatorStack.push(token)
      } else if (token === ',') {
        // 函数参数分隔符
        while (operatorStack.length > 0 && operatorStack[operatorStack.length - 1] !== '(') {
          outputQueue.push(operatorStack.pop())
        }
      } else if (this.isOperator(token)) {
        while (
          operatorStack.length > 0 &&
          this.isOperator(operatorStack[operatorStack.length - 1]) &&
          this.precedence[operatorStack[operatorStack.length - 1]] >= this.precedence[token]
        ) {
          outputQueue.push(operatorStack.pop())
        }
        operatorStack.push(token)
      } else if (token === '(') {
        operatorStack.push(token)
      } else if (token === ')') {
        while (operatorStack.length > 0 && operatorStack[operatorStack.length - 1] !== '(') {
          outputQueue.push(operatorStack.pop())
        }
        if (operatorStack.length === 0) {
          throw new Error('括号不匹配')
        }
        operatorStack.pop() // 移除 '('
        
        // 如果栈顶是函数，则弹出
        if (operatorStack.length > 0 && this.isFunction(operatorStack[operatorStack.length - 1])) {
          outputQueue.push(operatorStack.pop())
        }
      }
    }
    
    // 将剩余操作符弹出到输出队列
    while (operatorStack.length > 0) {
      const op = operatorStack.pop()
      if (op === '(' || op === ')') {
        throw new Error('括号不匹配')
      }
      outputQueue.push(op)
    }
    
    // 计算后缀表达式
    return this.evaluatePostfix(outputQueue)
  }
  
  /**
   * 分词
   */
  tokenize(expression) {
    const tokens = []
    let current = ''
    
    for (let i = 0; i < expression.length; i++) {
      const char = expression[i]
      
      if (/\s/.test(char)) {
        if (current) {
          tokens.push(current)
          current = ''
        }
        continue
      }
      
      if (/[+\-*/()^%,]/.test(char)) {
        if (current) {
          tokens.push(current)
          current = ''
        }
        
        // 处理负号
        if (char === '-' && (tokens.length === 0 || /[+\-*/()^%,]/.test(tokens[tokens.length - 1]))) {
          current = '-'
        } else {
          tokens.push(char)
        }
      } else {
        current += char
      }
    }
    
    if (current) {
      tokens.push(current)
    }
    
    return tokens
  }
  
  /**
   * 计算后缀表达式
   */
  evaluatePostfix(tokens) {
    const stack = []
    
    for (const token of tokens) {
      if (typeof token === 'number') {
        stack.push(token)
      } else if (this.isOperator(token)) {
        if (stack.length < 2) {
          throw new Error('表达式格式错误')
        }
        const b = stack.pop()
        const a = stack.pop()
        stack.push(this.applyOperator(a, b, token))
      } else if (this.isFunction(token)) {
        if (stack.length < 1) {
          throw new Error('函数参数不足')
        }
        const arg = stack.pop()
        stack.push(this.applyFunction(token, arg))
      }
    }
    
    if (stack.length !== 1) {
      throw new Error('表达式格式错误')
    }
    
    return stack[0]
  }
  
  /**
   * 应用运算符
   */
  applyOperator(a, b, operator) {
    switch (operator) {
      case '+': return a + b
      case '-': return a - b
      case '*': return a * b
      case '/': 
        if (b === 0) throw new Error('除零错误')
        return a / b
      case '%': return a % b
      case '^':
      case '**': return Math.pow(a, b)
      default: throw new Error(`未知运算符: ${operator}`)
    }
  }
  
  /**
   * 应用函数
   */
  applyFunction(funcName, arg) {
    if (!this.functions[funcName]) {
      throw new Error(`未知函数: ${funcName}`)
    }
    
    let processedArg = arg
    
    // 处理三角函数的角度模式
    if (['sin', 'cos', 'tan'].includes(funcName)) {
      processedArg = this.handleAngleMode(arg)
    }
    
    const result = this.functions[funcName](processedArg)
    
    // 处理反三角函数的角度模式
    if (['asin', 'acos', 'atan'].includes(funcName)) {
      return this.handleAngleMode(result, true)
    }
    
    return result
  }
  
  /**
   * 判断是否为数字
   */
  isNumber(token) {
    return !isNaN(parseFloat(token)) && isFinite(token)
  }
  
  /**
   * 判断是否为运算符
   */
  isOperator(token) {
    return this.precedence.hasOwnProperty(token)
  }
  
  /**
   * 判断是否为函数
   */
  isFunction(token) {
    return this.functions.hasOwnProperty(token)
  }
  
  /**
   * 格式化结果
   */
  formatResult(result) {
    // 处理非常小的数（接近0）
    if (Math.abs(result) < 1e-10) {
      return '0'
    }
    
    // 处理整数
    if (Number.isInteger(result)) {
      return result.toString()
    }
    
    // 处理小数
    const str = result.toString()
    if (str.includes('e')) {
      // 科学计数法
      return result.toExponential(6)
    } else {
      // 普通小数，最多保留10位小数
      return parseFloat(result.toFixed(10)).toString()
    }
  }
  
  /**
   * 添加到历史记录
   */
  addToHistory(expression, result) {
    this.history.unshift({
      expression,
      result,
      timestamp: new Date().toISOString()
    })
    
    // 只保留最近50条记录
    if (this.history.length > 50) {
      this.history = this.history.slice(0, 50)
    }
  }
  
  /**
   * 获取历史记录
   */
  getHistory() {
    return this.history
  }
  
  /**
   * 清空历史记录
   */
  clearHistory() {
    this.history = []
  }
  
  /**
   * 设置角度模式
   */
  setAngleMode(mode) {
    if (mode === 'deg' || mode === 'rad') {
      this.angleMode = mode
    }
  }
  
  /**
   * 获取角度模式
   */
  getAngleMode() {
    return this.angleMode
  }
  
  /**
   * 内存操作
   */
  memoryStore(value) {
    this.memory = value
  }
  
  memoryRecall() {
    return this.memory
  }
  
  memoryAdd(value) {
    this.memory += value
  }
  
  memorySubtract(value) {
    this.memory -= value
  }
  
  memoryClear() {
    this.memory = 0
  }
  
  /**
   * 获取内存值
   */
  getMemory() {
    return this.memory
  }
  
  /**
   * 验证表达式
   */
  validateExpression(expression) {
    try {
      // 检查括号匹配
      let parentheses = 0
      for (const char of expression) {
        if (char === '(') parentheses++
        if (char === ')') parentheses--
        if (parentheses < 0) return false
      }
      if (parentheses !== 0) return false
      
      // 检查是否有连续的运算符
      if (/[+\-*/^%]{2,}/.test(expression)) return false
      
      // 检查是否以运算符结尾（除了%和!）
      if (/[+\-*/^]$/.test(expression)) return false
      
      return true
    } catch (error) {
      return false
    }
  }
  
  /**
   * 获取函数列表
   */
  getFunctions() {
    return Object.keys(this.functions)
  }
  
  /**
   * 获取常数列表
   */
  getConstants() {
    return Object.keys(this.constants)
  }
}

// 创建全局实例
const calculator = new Calculator()

export default calculator
