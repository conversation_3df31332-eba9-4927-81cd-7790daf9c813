<template>
  <view class="page-container">
    <!-- 头部统计 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-content safe-area-inset-top">
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">¥{{ formatAmount(redPacketStore.totalAmount) }}</text>
            <text class="stat-label">总收益</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ redPacketStore.totalCount }}</text>
            <text class="stat-label">红包数量</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">¥{{ formatAmount(statistics.averageAmount) }}</text>
            <text class="stat-label">平均金额</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">¥{{ formatAmount(statistics.maxAmount) }}</text>
            <text class="stat-label">最大金额</text>
          </view>
        </view>
        
        <view class="today-stats">
          <text class="today-title">今日收益</text>
          <text class="today-amount">¥{{ formatAmount(statistics.todayAmount) }}</text>
          <text class="today-count">共{{ statistics.todayCount }}个红包</text>
        </view>
      </view>
    </view>
    
    <!-- 签到区域 -->
    <view class="signin-section">
      <view class="signin-card card" @tap="handleSignIn">
        <view class="signin-header">
          <text class="signin-title">每日签到</text>
          <view class="signin-status" :class="{ active: !redPacketStore.canSignInToday }">
            <text class="status-text">{{ redPacketStore.canSignInToday ? '可签到' : '已签到' }}</text>
          </view>
        </view>
        <view class="signin-info">
          <text class="signin-days">连续签到 {{ redPacketStore.signInDays }} 天</text>
          <text class="signin-reward">
            {{ redPacketStore.canSignInToday ? `签到可得¥${formatAmount(redPacketStore.nextSignInAmount)}` : '明日继续签到' }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 任务进度 -->
    <view class="tasks-section">
      <view class="section-title">
        <text class="title-text">每日任务</text>
        <text class="title-desc">完成任务获得红包奖励</text>
      </view>
      
      <view class="tasks-list">
        <view 
          v-for="(task, key) in taskCompletion" 
          :key="key"
          class="task-item card"
        >
          <view class="task-icon">{{ getTaskIcon(key) }}</view>
          <view class="task-info">
            <text class="task-name">{{ getTaskName(key) }}</text>
            <text class="task-desc">{{ getTaskDesc(key) }}</text>
            <view class="task-progress-bar">
              <view class="progress-bg">
                <view 
                  class="progress-fill" 
                  :style="{ width: (task.progress / task.total * 100) + '%' }"
                ></view>
              </view>
              <text class="progress-text">{{ task.progress }}/{{ task.total }}</text>
            </view>
          </view>
          <view class="task-reward">
            <text class="reward-amount">¥{{ formatAmount(task.reward[0]) }}-{{ formatAmount(task.reward[1]) }}</text>
            <view class="reward-status" :class="{ completed: task.completed }">
              <text class="status-text">{{ task.completed ? '已完成' : '进行中' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 红包记录 -->
    <view class="history-section">
      <view class="section-title">
        <text class="title-text">红包记录</text>
        <text class="title-desc">查看您的红包获得历史</text>
      </view>
      
      <view class="filter-tabs">
        <view 
          v-for="filter in filterTabs" 
          :key="filter.key"
          class="filter-tab"
          :class="{ active: currentFilter === filter.key }"
          @tap="setFilter(filter.key)"
        >
          <text class="tab-text">{{ filter.label }}</text>
        </view>
      </view>
      
      <view class="history-list" v-if="filteredHistory.length > 0">
        <view 
          v-for="packet in filteredHistory" 
          :key="packet.id"
          class="history-item card"
        >
          <view class="packet-icon" :style="{ backgroundColor: getRedPacketColor(packet.type) }">
            <text class="icon-text">🧧</text>
          </view>
          <view class="packet-info">
            <text class="packet-title">{{ formatTitle(packet.type, packet) }}</text>
            <text class="packet-desc">{{ packet.description }}</text>
            <text class="packet-time">{{ formatTime(packet.timestamp) }}</text>
          </view>
          <view class="packet-amount">
            <text class="amount-text">¥{{ formatAmount(packet.amount) }}</text>
            <view class="amount-status" :class="{ claimed: packet.claimed }">
              <text class="status-text">{{ packet.claimed ? '已到账' : '待领取' }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-else>
        <text class="empty-icon">📦</text>
        <text class="empty-text">暂无红包记录</text>
        <text class="empty-desc">快去使用工具获得红包吧！</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="actions-section">
      <button class="action-btn btn-primary" @tap="shareApp">
        <text class="btn-text">分享应用</text>
      </button>
      <button class="action-btn btn-outline" @tap="exportHistory">
        <text class="btn-text">导出记录</text>
      </button>
    </view>
    
    <!-- 红包弹窗 -->
    <RedPacketModal 
      :visible="redPacketStore.showRedPacketModal"
      :redpacket="redPacketStore.currentRedPacket"
      @close="redPacketStore.closeRedPacketModal"
      @opened="onRedPacketOpened"
    />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRedPacketStore } from '../../stores/redpacket.js'
import RedPacketModal from '../../components/RedPacketModal.vue'
import redPacketUtils from '../../utils/redpacket.js'
import audioManager from '../../utils/audio.js'

// Store
const redPacketStore = useRedPacketStore()

// 筛选状态
const currentFilter = ref('all')
const filterTabs = ref([
  { key: 'all', label: '全部' },
  { key: 'today', label: '今日' },
  { key: 'signIn', label: '签到' },
  { key: 'task', label: '任务' },
  { key: 'random', label: '随机' }
])

// 计算属性
const taskCompletion = computed(() => redPacketStore.taskCompletion)
const statistics = computed(() => redPacketUtils.getStatistics(redPacketStore.redPacketHistory))

const filteredHistory = computed(() => {
  let history = redPacketStore.redPacketHistory
  
  if (currentFilter.value === 'today') {
    const today = new Date().toDateString()
    history = history.filter(packet => 
      new Date(packet.timestamp).toDateString() === today
    )
  } else if (currentFilter.value !== 'all') {
    history = history.filter(packet => packet.type === currentFilter.value)
  }
  
  return history
})

// 页面加载
onMounted(() => {
  // 播放页面音效
  audioManager.playSound('page_turn')
})

// 签到处理
const handleSignIn = () => {
  if (!redPacketStore.canSignInToday) {
    uni.showToast({
      title: '今日已签到',
      icon: 'none'
    })
    return
  }
  
  const success = redPacketStore.signIn()
  if (success) {
    audioManager.playSound('success')
    uni.showToast({
      title: '签到成功！',
      icon: 'success'
    })
  }
}

// 设置筛选
const setFilter = (filter) => {
  currentFilter.value = filter
  audioManager.playSound('click')
}

// 分享应用
const shareApp = () => {
  redPacketStore.onAppShared()
  audioManager.playSound('button_click')
  
  // #ifdef MP-WEIXIN
  uni.shareAppMessage({
    title: '我在多功能工具箱获得了红包！',
    desc: '快来一起使用这个超实用的工具箱吧！',
    path: '/pages/index/index'
  })
  // #endif
  
  // #ifdef H5
  if (navigator.share) {
    navigator.share({
      title: '多功能工具箱',
      text: '我在多功能工具箱获得了红包！快来一起使用这个超实用的工具箱吧！',
      url: window.location.origin
    })
  } else {
    uni.showToast({
      title: '分享成功！',
      icon: 'success'
    })
  }
  // #endif
}

// 导出记录
const exportHistory = () => {
  try {
    const csvContent = redPacketUtils.exportRedPacketHistory(redPacketStore.redPacketHistory)
    
    // #ifdef H5
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `红包记录_${new Date().toLocaleDateString()}.csv`
    link.click()
    // #endif
    
    // #ifdef MP-WEIXIN
    uni.showModal({
      title: '导出记录',
      content: '小程序暂不支持文件导出，请在H5版本中使用此功能',
      showCancel: false
    })
    // #endif
    
    audioManager.playSound('success')
  } catch (error) {
    console.error('导出失败:', error)
    uni.showToast({
      title: '导出失败',
      icon: 'error'
    })
  }
}

// 红包开启回调
const onRedPacketOpened = (redpacket) => {
  console.log('红包已开启:', redpacket)
}

// 工具函数
const formatAmount = (amount) => redPacketUtils.formatAmount(amount)
const formatTime = (timestamp) => redPacketUtils.formatTime(timestamp)
const formatTitle = (type, packet) => redPacketUtils.formatTitle(type, packet)
const getRedPacketColor = (type) => redPacketUtils.getRedPacketColor(type)

const getTaskName = (taskType) => {
  const names = {
    useTools: '使用工具',
    playGames: '游戏娱乐', 
    shareApp: '分享应用'
  }
  return names[taskType] || taskType
}

const getTaskDesc = (taskType) => {
  const descs = {
    useTools: '使用任意工具功能',
    playGames: '进行游戏对局',
    shareApp: '分享应用给好友'
  }
  return descs[taskType] || ''
}

const getTaskIcon = (taskType) => {
  const icons = {
    useTools: '🔧',
    playGames: '🎮',
    shareApp: '📤'
  }
  return icons[taskType] || '📋'
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $bg-color-secondary;
}

.header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $gradient-secondary;
}

.header-content {
  position: relative;
  height: 100%;
  padding: 40rpx $spacing-md;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  display: block;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: $font-size-xs;
  opacity: 0.8;
  display: block;
}

.today-stats {
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: $border-radius-lg;
  padding: $spacing-md;
  backdrop-filter: blur(10px);
}

.today-title {
  font-size: $font-size-sm;
  opacity: 0.9;
  display: block;
  margin-bottom: 5rpx;
}

.today-amount {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  display: block;
  margin-bottom: 5rpx;
}

.today-count {
  font-size: $font-size-xs;
  opacity: 0.8;
  display: block;
}

.signin-section, .tasks-section, .history-section, .actions-section {
  padding: $spacing-md;
}

.signin-card {
  padding: $spacing-md;
}

.signin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-sm;
}

.signin-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.signin-status {
  padding: 4rpx 12rpx;
  border-radius: $border-radius-sm;
  background: $bg-color-light;
  
  &.active {
    background: $success-color;
  }
}

.status-text {
  font-size: $font-size-xs;
  color: $text-color-secondary;
  
  .active & {
    color: white;
  }
}

.signin-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.signin-days {
  font-size: $font-size-md;
  color: $text-color-primary;
  font-weight: $font-weight-medium;
}

.signin-reward {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.section-title {
  margin-bottom: $spacing-md;
}

.title-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.title-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.tasks-list, .history-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.task-item {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  gap: $spacing-md;
}

.task-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.task-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-sm;
}

.task-progress-bar {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.progress-bg {
  flex: 1;
  height: 8rpx;
  background: $bg-color-light;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: $primary-color;
  transition: width $transition-normal;
}

.progress-text {
  font-size: $font-size-xs;
  color: $text-color-light;
  min-width: 80rpx;
}

.task-reward {
  text-align: right;
}

.reward-amount {
  font-size: $font-size-sm;
  color: $warning-color;
  font-weight: $font-weight-medium;
  display: block;
  margin-bottom: 5rpx;
}

.reward-status {
  padding: 4rpx 8rpx;
  border-radius: $border-radius-sm;
  background: $bg-color-light;
  
  &.completed {
    background: $success-color;
  }
}

.filter-tabs {
  display: flex;
  margin-bottom: $spacing-md;
  background: white;
  border-radius: $border-radius-md;
  padding: 8rpx;
  box-shadow: $shadow-sm;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: $spacing-sm;
  border-radius: $border-radius-sm;
  
  &.active {
    background: $primary-color;
  }
}

.tab-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  
  .active & {
    color: white;
    font-weight: $font-weight-medium;
  }
}

.history-item {
  display: flex;
  align-items: center;
  padding: $spacing-md;
  gap: $spacing-md;
}

.packet-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: $border-radius-md;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: 40rpx;
}

.packet-info {
  flex: 1;
}

.packet-title {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  display: block;
  margin-bottom: 5rpx;
}

.packet-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
  margin-bottom: 5rpx;
}

.packet-time {
  font-size: $font-size-xs;
  color: $text-color-light;
  display: block;
}

.packet-amount {
  text-align: right;
}

.amount-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $danger-color;
  display: block;
  margin-bottom: 5rpx;
}

.amount-status {
  padding: 4rpx 8rpx;
  border-radius: $border-radius-sm;
  background: $bg-color-light;
  
  &.claimed {
    background: $success-color;
  }
}

.empty-state {
  text-align: center;
  padding: $spacing-2xl;
  color: $text-color-light;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: $spacing-md;
}

.empty-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  display: block;
  margin-bottom: $spacing-sm;
}

.empty-desc {
  font-size: $font-size-sm;
  display: block;
}

.actions-section {
  display: flex;
  gap: $spacing-md;
  padding-bottom: $spacing-xl;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
}
</style>
